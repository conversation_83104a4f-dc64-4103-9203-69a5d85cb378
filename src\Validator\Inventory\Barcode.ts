import { AdditionalInfo, AltSKU, Department_CRUD, Inventory, Inventory_In, Inventory_Vendors, InventoryVendor, Invoice_Itemized, Invoice_OnHold, Invoice_Totals, Kit_Index, PurchaseOrder, PurchaseOrderCreate, PurchaseOrderInsert, PurchaseOrderItem, PurchaseOrderItems, Setup_TS_Buttons, UpdatePurchaseOrder, UpdatePurchaseOrderItems, Vendor, VendorItem } from "../../server/types";

// Function to apply default values to inventoryData
export const applyDefaults = (data: Partial<Inventory>): Inventory => {
  return {
    ItemNum: data.ItemNum || '',
    ItemName: data.ItemName || '',
    Dept_ID: data.Dept_ID || '',
    Cost: data.Cost ?? 0,
    Price: data.Price ?? 0,
    Retail_Price: data.Retail_Price ?? 0,
    In_Stock: data.In_Stock ?? 0,
    Store_ID: data.Store_ID || '1001',
    Reorder_Level: data.Reorder_Level ?? 2,
    Reorder_Quantity: data.Reorder_Quantity ?? 12,
    Tax_1: data.Tax_1 ?? true,
    Tax_2: data.Tax_2 ?? false,
    Tax_3: data.Tax_3 ?? false,
    IsKit: data.IsKit ?? false,
    Vendor_Number: data.Vendor_Number,
    IsModifier: data.IsModifier ?? false,
    Kit_Override: data.Kit_Override ?? 0,
    Inv_Num_Barcode_Labels: data.Inv_Num_Barcode_Labels ?? 0,
    Use_Serial_Numbers: data.Use_Serial_Numbers ?? false,
    Num_Bonus_Points: data.Num_Bonus_Points ?? 0,
    IsRental: data.IsRental ?? false,
    Use_Bulk_Pricing: data.Use_Bulk_Pricing ?? false,
    Print_Ticket: data.Print_Ticket ?? false,
    Print_Voucher: data.Print_Voucher ?? false,
    Num_Days_Valid: data.Num_Days_Valid ?? 0,
    IsMatrixItem: data.IsMatrixItem ?? false,
    Vendor_Part_Num: data.Vendor_Part_Num,
    Location: data.Location || '',
    AutoWeigh: data.AutoWeigh ?? false,
    numBoxes: data.numBoxes ?? 0,
    Dirty: data.Dirty ?? true,
    Tear: data.Tear ?? 0,
    NumPerCase: data.NumPerCase ?? 0,
    FoodStampable: data.FoodStampable ?? false,
    ReOrder_Cost: data.ReOrder_Cost ?? 9.5,
    Helper_ItemNum: data.Helper_ItemNum || null,
    ItemName_Extra: data.ItemName_Extra || null,
    Exclude_Acct_Limit: data.Exclude_Acct_Limit ?? false,
    Check_ID: data.Check_ID ?? false,
    Old_InStock: data.Old_InStock ?? 0,
    Date_Created: data.Date_Created ?? new Date() ,
    ItemType: data.ItemType ?? 0,
    Prompt_Price: data.Prompt_Price ?? false,
    Prompt_Quantity: data.Prompt_Quantity ?? false,
    Inactive: data.Inactive ?? 0,
    Allow_BuyBack: data.Allow_BuyBack ?? true,
    Last_Sold: data.Last_Sold || null,
    Unit_Type: data.Unit_Type || null,
    Unit_Size: data.Unit_Size ?? 0,
    Fixed_Tax: data.Fixed_Tax ?? 0,
    DOB: data.DOB ?? 0,
    Special_Permission: data.Special_Permission ?? false,
    Prompt_Description: data.Prompt_Description ?? false,
    Check_ID2: data.Check_ID2 ?? false,
    Count_This_Item: data.Count_This_Item ?? true,
    Transfer_Cost_Markup: data.Transfer_Cost_Markup ?? 0,
    Print_On_Receipt: data.Print_On_Receipt ?? true,
    Transfer_Markup_Enabled: data.Transfer_Markup_Enabled ?? false,
    As_Is: data.As_Is ?? false,
    InStock_Committed: data.InStock_Committed ?? 0,
    RequireCustomer: data.RequireCustomer ?? false,
    PromptCompletionDate: data.PromptCompletionDate ?? false,
    PromptInvoiceNotes: data.PromptInvoiceNotes ?? false,
    Prompt_DescriptionOverDollarAmt: data.Prompt_DescriptionOverDollarAmt || null,
    Exclude_From_Loyalty: data.Exclude_From_Loyalty ?? false,
    BarTaxInclusive: data.BarTaxInclusive ?? false,
    ScaleSingleDeduct: data.ScaleSingleDeduct ?? false,
    GLNumber: data.GLNumber || '',
    ModifierType: data.ModifierType ?? 0,
    Position: data.Position ?? 0,
    numberOfFreeToppings: data.numberOfFreeToppings ?? 0,
    ScaleItemType: data.ScaleItemType ?? 0,
    DiscountType: data.DiscountType ?? 0,
    AllowReturns: data.AllowReturns ?? true,
    SuggestedDeposit: data.SuggestedDeposit || null,
    Liability: data.Liability ?? false,
    IsDeleted: data.IsDeleted ?? false,
    ItemLocale: data.ItemLocale || null,
    QuantityRequired: data.QuantityRequired || null,
    AllowOnDepositInvoices: data.AllowOnDepositInvoices ?? false,
    Import_Markup: data.Import_Markup ?? 0,
    PricePerMeasure: data.PricePerMeasure ?? 0,
    UnitMeasure: data.UnitMeasure || null,
    ShipCompliantProductType: data.ShipCompliantProductType || null,
    AlcoholContent: data.AlcoholContent || null,
    AvailableOnline: data.AvailableOnline ?? false,
    AllowOnFleetCard: data.AllowOnFleetCard ?? false,
    DoughnutTax: data.DoughnutTax ?? false,
    DisplayTaxInPrice: data.DisplayTaxInPrice || false,
    NeverPrintInKitchen: data.NeverPrintInKitchen ?? false,
    RowID: data.RowID || "13f2d89f-e93a-e511-8dc0-f0dde195483c",
    Tax_4: data.Tax_4 ?? false,
    Tax_5: data.Tax_5 ?? false,
    Tax_6: data.Tax_6 ?? false,
    DisableInventoryUpload: data.DisableInventoryUpload ?? false,
    InvoiceLimitQty: data.InvoiceLimitQty ?? 0,
    ItemCategory: data.ItemCategory ?? 0,
    IsRestrictedPerInvoice: data.IsRestrictedPerInvoice ?? false,
    TagStatus: data.TagStatus || null,
  };
};

export const applyDefaultsInventoryAdjust = (data: Partial<Inventory_In>): Inventory_In => {
  return {
    ItemNum: data.ItemNum || '',
    Store_ID: data.Store_ID || '',
    Quantity: data.Quantity || 0,
    CostPer: data.CostPer || 0,
    DateTime: data.DateTime || '',
    Vendor_Number: data.Vendor_Number || '',
    Dirty: data.Dirty !== undefined ? data.Dirty : false,
    TransType: data.TransType || '',
    Destination: data.Destination || '',
    Description: data.Description || '',
    Cashier_ID: data.Cashier_ID || '',
    PO_Number: data.PO_Number || 0,
    Delivery_Number: data.Delivery_Number || '',
  };
};

export const applyDefaultsSetupTSButtons = (data: Partial<Setup_TS_Buttons>): Setup_TS_Buttons => {
  return {
    Store_ID: data.Store_ID || '',
    Station_ID: data.Station_ID || '',
    Index: data.Index || 0,
    Caption: data.Caption || '',
    Picture: data.Picture || '',
    Function: data.Function || 0,
    Option1: data.Option1 || '',
    BackColor: data.BackColor || 0,
    ForeColor: data.ForeColor !== undefined ? data.ForeColor : 0, // Handle null value explicitly
    Visible: data.Visible !== undefined ? data.Visible : true,  // Handle optional boolean explicitly
    BtnType: data.BtnType || 0,
    Ident: data.Ident || '',
    ScheduleIndex: data.ScheduleIndex || 0,
    Option2: data.Option2 || '',
    Option3: data.Option3 || '',
    Option4: data.Option4 !== undefined ? data.Option4 : false, // Handle optional boolean explicitly
    HideCaption: data.HideCaption !== undefined ? data.HideCaption : false, // Handle optional boolean explicitly
  };
};

export const applyDefaultsKitIndex = (data: Partial<Kit_Index>): Kit_Index => {
  return {
    Kit_ID: data.Kit_ID || '',
    Store_ID: data.Store_ID || '',
    ItemNum: data.ItemNum || '',
    Discount: data.Discount || 0,
    Quantity: data.Quantity || 1,
    Index: data.Index || 0,
    Price: data.Price || 0,
    Description: data.Description || '',
    InvoiceMethodToUse: data.InvoiceMethodToUse || 0,
    ChoiceLockdown: data.ChoiceLockdown || 0,
  };
};

export const applyDefaultsDepartment = (data: Partial<Department_CRUD>): Department_CRUD => {
  return {
    Dept_ID: data.Dept_ID || '',
    Store_ID: data.Store_ID || '',
    Description: data.Description || '',
    Type: data.Type || 0,
    TSDisplay: data.TSDisplay || false,
    Cost_MarkUp: data.Cost_MarkUp || 0,
    Dirty: data.Dirty || false,
    SubType: data.SubType || '',
    Print_Dept_Notes: data.Print_Dept_Notes || false,
    Dept_Notes: data.Dept_Notes || '',
    Require_Permission: data.Require_Permission || false,
    Require_Serials: data.Require_Serials || false,
    BarTaxInclusive: data.BarTaxInclusive || false,
    Cost_Calculation_Percentage: data.Cost_Calculation_Percentage || 0,
    Square_Footage: data.Square_Footage || 0,
    AvailableOnline: data.AvailableOnline || false,
    IncludeInScaleExport: data.IncludeInScaleExport || false,
  };
};


export const applyDefaultsAddition = (data: Partial<AdditionalInfo>): AdditionalInfo => {
  
  return {
    Store_ID: data.Store_ID || '',
    ItemNum: data.ItemNum || '',
    ExtendedDescription: data.ExtendedDescription || '',
    Keywords: data.Keywords || '',
    Brand: data.Brand || '',
    Theme: data.Theme || '',
    SubCategory: data.SubCategory || '',
    LeadTime: data.LeadTime || '',
    ProductOnPromotionPreOrder: data.ProductOnPromotionPreOrder || false,
    ProductOnSpecialOffer: data.ProductOnSpecialOffer || false,
    NewProduct: data.NewProduct || false,
    Discountable: data.Discountable || false,
    WebPrice: data.WebPrice || 0,
    ReleaseDate: data.ReleaseDate || null,
    Weight: data.Weight || 0,
    NoWebSales: data.NoWebSales || false,
    IsPrimaryMatrixItem: data.IsPrimaryMatrixItem || false,
    Priority: data.Priority || 0,
    Rating: data.Rating || 0,
    CustomNumber1: data.CustomNumber1 || 0,
    CustomNumber2: data.CustomNumber2 || 0,
    CustomNumber3: data.CustomNumber3 || 0,
    CustomNumber4: data.CustomNumber4 || 0,
    CustomNumber5: data.CustomNumber5 || 0,
    CustomText1: data.CustomText1 || '',
    CustomText2: data.CustomText2 || '',
    CustomText3: data.CustomText3 || '',
    CustomText4: data.CustomText4 || '',
    CustomText5: data.CustomText5 || '',
    CustomExtendedText1: data.CustomExtendedText1 || '',
    CustomExtendedText2: data.CustomExtendedText2 || '',
    SubDescription1: data.SubDescription1 || '',
    SubDescription2: data.SubDescription2 || '',
    SubDescription3: data.SubDescription3 || ''
  };
}

export const applyDefaultsInvoiceTotals = (data: Partial<Invoice_Totals>): Invoice_Totals => {
  return {
    Invoice_Number: data.Invoice_Number || 0,
    Store_ID: data.Store_ID || '',
    CustNum: data.CustNum || '',
    DateTime: data.DateTime || new Date(),
    Total_Cost: data.Total_Cost || 0,
    Discount: data.Discount || 0,
    Total_Price: data.Total_Price || 0,
    Total_Tax1: data.Total_Tax1 || 0,
    Total_Tax2: data.Total_Tax2 || 0,
    Total_Tax3: data.Total_Tax3 || 0,
    Grand_Total: data.Grand_Total || 0,
    Amt_Tendered: data.Amt_Tendered || 0,
    Amt_Change: data.Amt_Change || 0,
    ShipToUsed: data.ShipToUsed !== undefined ? data.ShipToUsed : false,
    InvoiceNotesUsed: data.InvoiceNotesUsed !== undefined ? data.InvoiceNotesUsed : false,
    Status: data.Status || '',
    Cashier_ID: data.Cashier_ID || '',
    Station_ID: data.Station_ID || '',
    Payment_Method: data.Payment_Method || '',
    Acct_Balance_Due: data.Acct_Balance_Due || 0,
    Acct_FullyPaid_Date: data.Acct_FullyPaid_Date || null,
    Taxed_1: data.Taxed_1 || 0,
    Taxed_Sales: data.Taxed_Sales || 0,
    NonTaxed_Sales: data.NonTaxed_Sales || 0,
    Tax_Exempt_Sales: data.Tax_Exempt_Sales || 0,
    CA_Amount: data.CA_Amount || 0,
    CH_Amount: data.CH_Amount || 0,
    CC_Amount: data.CC_Amount || 0,
    OA_Amount: data.OA_Amount || 0,
    GC_Amount: data.GC_Amount || 0,
    Tip_Amount: data.Tip_Amount || 0,
    Old_Balance: data.Old_Balance || 0,
    Num_People_Party: data.Num_People_Party || 0,
    AcctBalanceBefore: data.AcctBalanceBefore || 0,
    Salesperson: data.Salesperson || '',
    Dirty: data.Dirty !== undefined ? data.Dirty : false,
    Zip_Code: data.Zip_Code || '',
    InvType: data.InvType || '0',
    FS_Amount: data.FS_Amount || 0,
    Amt_FS_AmtTend: data.Amt_FS_AmtTend || 0,
    Amt_FS_Change: data.Amt_FS_Change || 0,
    DC_Amount: data.DC_Amount || 0,
    OA_Amount_Limited: data.OA_Amount_Limited || 0,
    Cost_Center_Index: data.Cost_Center_Index || 0,
    Orig_OnHoldID: data.Orig_OnHoldID || null,
    Total_FixedTax: data.Total_FixedTax || 0,
    Total_GC_Sold: data.Total_GC_Sold || 0,
    Tax_Rate_ID: data.Tax_Rate_ID || 0,
    Tax_Rate1_Percent: data.Tax_Rate1_Percent || 0,
    Amt_CA_Sec: data.Amt_CA_Sec || 0,
    Exchange_Rate: data.Exchange_Rate || 0,
    IsLayaway: data.IsLayaway !== undefined ? data.IsLayaway : false,
    Amt_Deposit: data.Amt_Deposit || 0,
    LAY_Amount: data.LAY_Amount || 0,
    Total_GC_Free: data.Total_GC_Free || 0,
    MacromatixSyncStatus: data.MacromatixSyncStatus || 0,
    TotalLiability: data.TotalLiability || 0,
    ReferenceInvoiceNumber: data.ReferenceInvoiceNumber || null,
    CourseOrderingProgress: data.CourseOrderingProgress || '',
    Amt_CA_Sec_Tendered: data.Amt_CA_Sec_Tendered || 0,
    OnlineOrderID: data.OnlineOrderID || '',
    OrderSource: data.OrderSource || 0,
    OP_Amount: data.OP_Amount || 0,
    MP_Amount: data.MP_Amount || 0,
    TaxCategory: data.TaxCategory || 0,
    MPDiscount_Amount: data.MPDiscount_Amount || 0,
    Donation_Amount: data.Donation_Amount || 0,
    Total_UndiscountedSale: data.Total_UndiscountedSale || 0,
    EBTCashBenefit_Amount: data.EBTCashBenefit_Amount || 0,
    Split_Check_Type: data.Split_Check_Type || 0,
    OnlineLoyalty_Contact_ID: data.OnlineLoyalty_Contact_ID || '',
    Total_Tax4: data.Total_Tax4 || 0,
    Total_Tax5: data.Total_Tax5 || 0,
    Total_Tax6: data.Total_Tax6 || 0,
    AgeVerificationMethod: data.AgeVerificationMethod || 0,
    AgeVerification: data.AgeVerification || 0,
    CP_Amount: data.CP_Amount || 0,
  };
};

export const applyDefaultsInvoiceItemized = (data: Partial<Invoice_Itemized>): Invoice_Itemized => {
  return {
    Invoice_Number: data.Invoice_Number || 0,
    ItemNum: data.ItemNum || '',
    Quantity: data.Quantity || 0,
    CostPer: data.CostPer || 0,
    PricePer: data.PricePer || 0,
    Tax1Per: data.Tax1Per || 0,
    Tax2Per: data.Tax2Per || 0,
    Tax3Per: data.Tax3Per || 0,
    Serial_Num: data.Serial_Num !== undefined ? data.Serial_Num : false,
    Kit_ItemNum: data.Kit_ItemNum || '',
    BC_Invoice_Number: data.BC_Invoice_Number || 0,
    LineDisc: data.LineDisc || 0,
    DiffItemName: data.DiffItemName || '',
    NumScans: data.NumScans || 0,
    numBonus: data.numBonus || 0,
    Line_Tax_Exempt: data.Line_Tax_Exempt !== undefined ? data.Line_Tax_Exempt : false,
    Commission: data.Commission || 0,
    Store_ID: data.Store_ID || '',
    origPricePer: data.origPricePer || 0,
    Allow_Discounts: data.Allow_Discounts !== undefined ? data.Allow_Discounts : true,
    Person: data.Person || '',
    Sale_Type: data.Sale_Type || 0,
    Ticket_Number: data.Ticket_Number || '',
    IsRental: data.IsRental !== undefined ? data.IsRental : false,
    FixedTaxPer: data.FixedTaxPer || 0,
    GC_Sold: data.GC_Sold || 0,
    Special_Price_Lock: data.Special_Price_Lock !== undefined ? data.Special_Price_Lock : false,
    As_Is: data.As_Is !== undefined ? data.As_Is : false,
    Returned: data.Returned !== undefined ? data.Returned : false,
    DOB: data.DOB || 0,
    UserDefined: data.UserDefined || '',
    Cashier_ID_Itemized: data.Cashier_ID_Itemized || '',
    IsLayaway: data.IsLayaway !== undefined ? data.IsLayaway : false,
    ReturnedQuantity: data.ReturnedQuantity || 0,
    GC_Free: data.GC_Free || 0,
    ScaleItemType: data.ScaleItemType || 0,
    ParentObjectID: data.ParentObjectID || '',
    BulkRate: data.BulkRate || '',
    SecurityDeposit: data.SecurityDeposit || 0,
    Liability: data.Liability || 0,
    SalePricePer: data.SalePricePer || 0,
    Line_Tax_Exempt_2: data.Line_Tax_Exempt_2 !== undefined ? data.Line_Tax_Exempt_2 : false,
    Line_Tax_Exempt_3: data.Line_Tax_Exempt_3 !== undefined ? data.Line_Tax_Exempt_3 : false,
    modifierPriceLock: data.modifierPriceLock !== undefined ? data.modifierPriceLock : false,
    Salesperson: data.Salesperson || '',
    ComboApplied: data.ComboApplied !== undefined ? data.ComboApplied : false,
    KitchenQuantityPrinted: data.KitchenQuantityPrinted || 0,
    PricePerBeforeDiscount: data.PricePerBeforeDiscount || 0,
    OrigPriceSetBy: data.OrigPriceSetBy || 0,
    PriceChangedBy: data.PriceChangedBy || 0,
    Kit_Override: data.Kit_Override || 0,
    KitTotal: data.KitTotal || 0,
    SentToKitchen: data.SentToKitchen !== undefined ? data.SentToKitchen : false,
    OnlineLoyalty_OfferId: data.OnlineLoyalty_OfferId || '',
    Tax4Per: data.Tax4Per || 0,
    Tax5Per: data.Tax5Per || 0,
    Tax6Per: data.Tax6Per || 0,
    Line_Tax_Exempt_4: data.Line_Tax_Exempt_4 !== undefined ? data.Line_Tax_Exempt_4 : false,
    Line_Tax_Exempt_5: data.Line_Tax_Exempt_5 !== undefined ? data.Line_Tax_Exempt_5 : false,
    Line_Tax_Exempt_6: data.Line_Tax_Exempt_6 !== undefined ? data.Line_Tax_Exempt_6 : false,
    Tare: data.Tare || 0,
  };
};

export const applyDefaultsInvoiceOnHold = (data: Partial<Invoice_OnHold>): Invoice_OnHold => {
  return {
    Invoice_Number: data.Invoice_Number || 0,
    OnHoldID: data.OnHoldID || '',
    Cashier_ID: data.Cashier_ID || '',
    Store_ID: data.Store_ID || '',
    Occupied: data.Occupied !== undefined ? data.Occupied : false,
    Section_ID: data.Section_ID || '',
    Status: data.Status || 0,
    Identifier: data.Identifier || '',
    PreAuthorized: data.PreAuthorized !== undefined ? data.PreAuthorized : false,
    Name: data.Name || '',
    Station_ID: data.Station_ID || '',
  };
};

export const applyDefaultsPurchaseOrder = (data: Partial<PurchaseOrder>): PurchaseOrder => {
  return {
    PO_Number: data.PO_Number || 0,
    Store_ID: data.Store_ID || '',
    DateTime: data.DateTime || '',
    Reference: data.Reference || '',
    Vendor_Number: data.Vendor_Number || '',
    Total_Cost: data.Total_Cost || 0,
    Total_Cost_Received: data.Total_Cost_Received || 0,
    Terms: data.Terms || '',
    Due_Date: data.Due_Date || '',
    Ship_Via: data.Ship_Via || '',
    ShipTo_1: data.ShipTo_1 || '',
    ShipTo_2: data.ShipTo_2 || '',
    ShipTo_3: data.ShipTo_3 || '',
    ShipTo_4: data.ShipTo_4 || '',
    ShipTo_5: data.ShipTo_5 || '',
    Instructions: data.Instructions || '',
    Status: data.Status || '',
    Last_Modified: data.Last_Modified || '',
    Dirty: data.Dirty ?? false,  // Default to `false` if `undefined`
    Cashier_ID: data.Cashier_ID || '',
    Billable_Department: data.Billable_Department || '',
    ShipTo_Destination: data.ShipTo_Destination || '',
    Ordering_Mode: data.Ordering_Mode || 0,
    Fully_Authorized: data.Fully_Authorized ?? false,  // Default to `false` if `undefined`
    Print_Notes_On_PO: data.Print_Notes_On_PO ?? false,  // Default to `false` if `undefined`
    Cancel_Date: data.Cancel_Date || '',
    Total_Charges: data.Total_Charges || 0,
    Fully_Paid: data.Fully_Paid ?? false,  // Default to `false` if `undefined`
    POType: data.POType || 0,
    ExpectedAmountToReceive: data.ExpectedAmountToReceive || 0,
    Order_Reason: data.Order_Reason || '',
    Distributor: data.Distributor || '',
    First_Name: data.First_Name || '',
    Last_Name: data.Last_Name || '',
    Company: data.Company || '',
    Address_1: data.Address_1 || '',
    Address_2: data.Address_2 || '',
    City: data.City || '',
    State: data.State || '',
    Zip_Code: data.Zip_Code || '',
    Phone: data.Phone || '',
    Fax: data.Fax || '',
    Vendor_Tax_ID: data.Vendor_Tax_ID || '',
    Vendor_Terms: data.Vendor_Terms || '',
    SSN: data.SSN || '',
    Commission: data.Commission || 0,
    Rent: data.Rent || 0,
    County: data.County || '',
    Country: data.Country || '',
    Email: data.Email || '',
    Website: data.Website || '',
    Minimum_Order: data.Minimum_Order || 0,
    Default_Ordering_Mode: data.Default_Ordering_Mode || 0,
    Default_Billable_Department: data.Default_Billable_Department || '',
    Default_PO_Delivery: data.Default_PO_Delivery || 0,
  };
};


export const applyDefaultsVendor = (data: Partial<Vendor>): Vendor => {
  return {
    Vendor_Number: data.Vendor_Number || '',
    First_Name: data.First_Name || '',
    Last_Name: data.Last_Name || '',
    Company: data.Company || '',
    Address_1: data.Address_1 || '',
    Address_2: data.Address_2 || '',
    City: data.City || '',
    State: data.State || '',
    Zip_Code: data.Zip_Code || '',
    Phone: data.Phone || '',
    Fax: data.Fax || '',
    Vendor_Tax_ID: data.Vendor_Tax_ID || '',
    Vendor_Terms: data.Vendor_Terms || '',
    SSN: data.SSN || '',
    Commission: data.Commission || 0,
    Rent: data.Rent || 0,
    Dirty: data.Dirty ?? true,  // Default to `false` if `undefined`
    County: data.County || '',
    Country: data.Country || '',
    Email: data.Email || '',
    Website: data.Website || '',
    Minimum_Order: data.Minimum_Order || 0,
    Default_Ordering_Mode: data.Default_Ordering_Mode || 0,
    Default_Billable_Department: data.Default_Billable_Department || '',
    Default_PO_Delivery: data.Default_PO_Delivery || 0,
  };
};

export const applyDefaultsPurchaseOrderItem = (data: Partial<PurchaseOrderItems>): PurchaseOrderItems => {
  return {
    PO_Number: data.PO_Number || 0,
    ItemNum: data.ItemNum || '',
    Quan_Ordered: data.Quan_Ordered || 0,
    CostPer: data.CostPer || 0,
    Quan_Received: data.Quan_Received || 0,
    Vendor_Part_Number: data.Vendor_Part_Number || '',
    CasePack: data.CasePack || 0,
    Store_ID: data.Store_ID || '',
    destStore_ID: data.destStore_ID || '',
    Current_Batch_Quan: data.Current_Batch_Quan || 0,
    Quan_Damaged: data.Quan_Damaged || 0,
    Reason: data.Reason || '',
    NumberPerCase: data.NumberPerCase || 0,
    OverrideCommission: data.OverrideCommission || false,
    Quan_OutofDate: data.Quan_OutofDate || 0,
    ItemName: data.ItemName || '',
  };
};

export const applyDefaultsInventoryVendorsUpdate = (data: Partial<Inventory_Vendors>): Inventory_Vendors => {
  return {
    ItemNum: data.ItemNum || '',
    Store_ID: data.Store_ID || '',
    Vendor_Number: data.Vendor_Number || '',
    CostPer: data.CostPer || 0,
    Case_Cost: data.Case_Cost || 0,
    NumPerVenCase: data.NumPerVenCase || 0,
    Vendor_Part_Num: data.Vendor_Part_Num || '',
    CubeCost: data.CubeCost || 0,
    WeightCost: data.WeightCost || 0,
    OverrideCommission: data.OverrideCommission || false,
    LandedCost: data.LandedCost || 0,
  };
};


export const applyDefaultsPurchaseOrderItemUpdate = (data: Partial<UpdatePurchaseOrderItems>): UpdatePurchaseOrderItems => {
  return {
    PO_Number: data.PO_Number || 0,
    ItemNum: data.ItemNum || '',
    Quan_Ordered: data.Quan_Ordered || 0,
    CostPer: data.CostPer || 0,
    Quan_Received: data.Quan_Received || 0,
    Vendor_Part_Number: data.Vendor_Part_Number || '',
    CasePack: data.CasePack || 0,
    Store_ID: data.Store_ID || '',
    destStore_ID: data.destStore_ID || '',
    Current_Batch_Quan: data.Current_Batch_Quan || 0,
    Quan_Damaged: data.Quan_Damaged || 0,
    Reason: data.Reason || '',
    NumberPerCase: data.NumberPerCase || 0,
    OverrideCommission: data.OverrideCommission || false,
    Quan_OutofDate: data.Quan_OutofDate || 0,
  };
};
export const applyDefaultsVendorItem = (data: Partial<VendorItem>): VendorItem => {
  return {
    ItemNum: data.ItemNum || '',
    Store_ID: data.Store_ID || '',
    Vendor_Number: data.Vendor_Number || '',
    CostPer: data.CostPer || 0,
    Case_Cost: data.Case_Cost || 0,
    NumPerVenCase: data.NumPerVenCase || 0,
    Vendor_Part_Num: data.Vendor_Part_Num || '',
    CubeCost: data.CubeCost || 0,
    WeightCost: data.WeightCost || 0,
    OverrideCommission: data.OverrideCommission || false,
    LandedCost: data.LandedCost || 0,
    ItemName: data.ItemName || '',
  };
};

export const applyDefaultsUpdatePurchaseOrder = (data: Partial<UpdatePurchaseOrder>): UpdatePurchaseOrder => {
  return {
    PO_Number: data.PO_Number || 0,
    Store_ID: data.Store_ID || '',
    DateTime: data.DateTime || '',
    Reference: data.Reference || '',
    Vendor_Number: data.Vendor_Number || '',
    Total_Cost: data.Total_Cost || 0,
    Total_Cost_Received: data.Total_Cost_Received || 0,
    Terms: data.Terms || '',
    Due_Date: data.Due_Date || '',
    Ship_Via: data.Ship_Via || '',
    ShipTo_1: data.ShipTo_1 || '',
    ShipTo_2: data.ShipTo_2 || '',
    ShipTo_3: data.ShipTo_3 || '',
    ShipTo_4: data.ShipTo_4 || '',
    ShipTo_5: data.ShipTo_5 || '',
    Instructions: data.Instructions || '',
    Status: data.Status || '',
    Last_Modified: data.Last_Modified || '',
    Dirty: data.Dirty ?? false,
    Cashier_ID: data.Cashier_ID || '',
    Billable_Department: data.Billable_Department || '',
    ShipTo_Destination: data.ShipTo_Destination || '',
    Ordering_Mode: data.Ordering_Mode || 0,
    Fully_Authorized: data.Fully_Authorized ?? false,
    Print_Notes_On_PO: data.Print_Notes_On_PO ?? false,
    Cancel_Date: data.Cancel_Date || '',
    Total_Charges: data.Total_Charges || 0,
    Fully_Paid: data.Fully_Paid ?? false,
    POType: data.POType || 0,
    ExpectedAmountToReceive: data.ExpectedAmountToReceive || 0,
    Order_Reason: data.Order_Reason || '',
    Distributor: data.Distributor || ''
  };
};

export const applyDefaultsAltSKU = (data: Partial<AltSKU>): AltSKU => {
  return {
    Store_ID: data.Store_ID || '',
    ItemNum: data.ItemNum || '',
    AltSKU: data.AltSKU || '',
  };
};

export const applyDefaultsInventoryVendor = (data: Partial<InventoryVendor>): InventoryVendor => {
  return {
    ItemNum: data.ItemNum || '',
    Store_ID: data.Store_ID || '',
    Vendor_Number: data.Vendor_Number || '',
    CostPer: data.CostPer || 0,
    Case_Cost: data.Case_Cost || 0,
    NumPerVenCase: data.NumPerVenCase || 0,
    Vendor_Part_Num: data.Vendor_Part_Num || '',
    CubeCost: data.CubeCost || 0,
    WeightCost: data.WeightCost || 0,
    OverrideCommission: data.OverrideCommission !== undefined ? data.OverrideCommission : false,
    LandedCost: data.LandedCost || 0,
  };
};



