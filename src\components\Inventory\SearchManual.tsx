import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Platform,
  Keyboard,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {useFocusEffect, useIsFocused} from '@react-navigation/native';
import Feather from 'react-native-vector-icons/Feather';
import AntDesign from 'react-native-vector-icons/AntDesign';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts} from '../../styles/fonts';
import {MaterialColors} from '../../constants/MaterialColors';
import Animated, {
  useAnimatedStyle,
  withTiming,
  useSharedValue,
} from 'react-native-reanimated';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {Primary} from '../../constants/Color';
import {useThemeColors} from '../../Theme/useThemeColors';
import {useTheme} from '../../Theme/ThemeContext';

interface SearchProps {
  PlaceHolder?: string;
  onChangeText?: (text: string) => void;
  Value?: string | number | null;
  Title?: string;
  Editable?: boolean;
  isNumeric?: boolean;
  error?: string;
  touched?: boolean;
  AutoFocus?: boolean;
  onBlur?: (e: any) => void;
  maxLength?: number;
  keyboardON?: boolean;
  isFilter?: boolean;
  textInputRef?: React.RefObject<TextInput>;
  clearInputTrigger?: boolean;
  isBarcode?: boolean;
  onToggleLookup?: (value: boolean) => void;
  OnSubmitEditing?: () => void;
  ClearInput?: () => void;
}

const SearchManual = ({
  PlaceHolder,
  onChangeText,
  Value,
  Editable = true,
  isNumeric = false,
  keyboardON = false,
  error,
  onBlur,
  maxLength,
  isFilter = false,
  textInputRef,
  clearInputTrigger,
  isBarcode = false,
  AutoFocus = true,
  onToggleLookup,
  OnSubmitEditing,
  ClearInput,
}: SearchProps) => {
  const [inputValue, setInputValue] = useState(Value?.toString() ?? '');
  const [togglingIcon, setTogglingIcon] = useState(false);
  const isFocused = useIsFocused();
  const [clear, setClear] = useState<boolean>(false);
  const [keyboardProcessing, setKeyboardProcessing] = useState<boolean>(false);
  useEffect(() => {
    setInputValue(Value?.toString() ?? '');
  }, [Value]);
  // Focus the input when the screen is focused

  useEffect(() => {
    if (clearInputTrigger) {
      handleDismissKeyboard();
      if (!isBarcode) {
        setInputValue('');
      }
      textInputRef?.current?.focus(); // Clear input when trigger is true
    }
  }, [clearInputTrigger]);

  // Handle the clear input action
  const handleClearInput = () => {
    if (!isBarcode) {
      setInputValue('');
    }
    setClear(false);
    if (onChangeText) {
      onChangeText('');
    }
  };

  const handleDismissKeyboard = () => {
    Keyboard.dismiss();
  };
  useEffect(() => {
    if (!isBarcode) {
      setInputValue(Value?.toString() ?? '');
    }
  }, [Value]);

  useFocusEffect(
    useCallback(() => {
      setInputValue('');
    }, []),
  );

  const KeyboardToggleButton = ({isOn, onToggle}) => {
    const colors = useThemeColors(); // Add this hook
    const [isProcessing, setIsProcessing] = useState(false);

    const handlePress = () => {
      if (isProcessing) return;

      setIsProcessing(true);
      onToggle(!isOn);

      setTimeout(() => {
        setIsProcessing(false);
      }, 300);
    };

    return (
      <TouchableOpacity
        style={{
          padding: 0,
          backgroundColor: 'transparent',
          borderRadius: 8,
        }}
        activeOpacity={0.6}
        hitSlop={{top: 20, right: 20, bottom: 20, left: 20}}
        onPress={handlePress}>
        {isOn ? (
          <MaterialCommunityIcons
            name="keyboard-close"
            size={hp('3.5%')}
            color={colors.primary}
          />
        ) : (
          <MaterialCommunityIcons
            name="keyboard"
            size={hp('3.5%')}
            color={colors.primary}
          />
        )}
      </TouchableOpacity>
    );
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: wp('3%'),
      paddingVertical: hp('1.2%'),
      width: '87%',
      borderRadius: 25,
      borderWidth: 1,
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: {width: 0, height: 1},
          shadowOpacity: isDark ? 0.3 : 0.1,
          shadowRadius: 3,
        },
        android: {
          elevation: 2,
        },
      }),
    },
    filterContainer: {
      flex: 1,
    },
    focusedContainer: {
      ...Platform.select({
        ios: {
          shadowOffset: {width: 0, height: 1},
          shadowOpacity: isDark ? 0.4 : 0.2,
          shadowRadius: 4,
        },
        android: {
          elevation: 3,
        },
      }),
    },
    inputWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    input: {
      fontSize: hp('1.8%'),
      fontFamily: Fonts.OnestMedium,
      marginLeft: wp('2%'),
      paddingVertical: hp('0.2%'),
      flex: 1,
    },
    keyboardButton: {
      paddingHorizontal: 8,
      marginLeft: 8,
    },
    clearButton: {
      backgroundColor: colors.disabled,
      width: hp('3.2%'),
      height: hp('3.2%'),
      borderRadius: hp('1.6%'),
      justifyContent: 'center',
      alignItems: 'center',
    },
  });

  return (
    <View style={{flexDirection: 'row', alignItems: 'center'}}>
      <View
        style={[
          styles.container,
          {backgroundColor: colors.surface, borderColor: colors.border},
          isFilter && styles.filterContainer,
          isFocused && [
            styles.focusedContainer,
            {
              borderColor: colors.primary,
              shadowColor: colors.primary,
            },
          ],
        ]}>
        <View style={styles.inputWrapper}>
          <Feather
            name="search"
            color={isFocused ? colors.primary : colors.textSecondary}
            size={hp('2.3%')}
          />

          <TextInput
            placeholder={PlaceHolder}
            style={[styles.input, {color: colors.text}]}
            placeholderTextColor={colors.placeholder}
            value={inputValue}
            editable={Editable}
            showSoftInputOnFocus={keyboardON}
            onChangeText={text => {
              setInputValue(text);
              setClear(true);
              if (onChangeText) onChangeText(text);
            }}
            keyboardType={isNumeric ? 'numeric' : 'default'}
            onBlur={e => {
              if (onBlur) {
                onBlur(e);
              }
            }}
            maxLength={maxLength}
            autoFocus={AutoFocus}
            ref={textInputRef}
            onSubmitEditing={OnSubmitEditing}
            returnKeyType="done"
          />
          {inputValue.length > 0 && (
            <TouchableOpacity onPress={ClearInput}>
              <MaterialCommunityIcons
                name="close-circle"
                size={hp('2.5%')}
                color={colors.primary}
              />
            </TouchableOpacity>
          )}
        </View>
      </View>
      <View style={{marginLeft: 10}}>
        <KeyboardToggleButton
          isOn={keyboardON}
          onToggle={newState => {
            onToggleLookup?.(newState);
          }}
        />
      </View>
    </View>
  );
};

export default SearchManual;
