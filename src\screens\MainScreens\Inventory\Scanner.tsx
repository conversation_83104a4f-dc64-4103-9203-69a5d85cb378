import {
  View,
  Text,
  StyleSheet,
  Alert,
  ActivityIndicator,
  TouchableOpacity,
  TextInput,
  Modal,
  StatusBar,
} from 'react-native';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {useCodeScanner} from 'react-native-vision-camera';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useFocusEffect} from '@react-navigation/native';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../../styles/fonts';
import {MaterialColors} from '../../../constants/MaterialColors';
import Search from '../../../components/Inventory/Search';
import AppScanner from '../../../components/Inventory/AppScanner';
import {
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
} from '../../../utils/PublicHelper';
import {getInventoryPort} from '../../../server/InstanceTypes';
import {AdditionalInfo, InventoryVendor} from '../../../server/types';
import FontAwesome5Icon from 'react-native-vector-icons/FontAwesome5';
import SearchManual from '../../../components/Inventory/SearchManual';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

type NavProps = {
  navigation: NativeStackNavigationProp<any>;
};

const Scanner: React.FC<NavProps> = ({navigation}) => {
  const [camera, setCamera] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isAlertVisible, setIsAlertVisible] = useState(false);
  const [search, setSearch] = useState<string>('');
  const [inventoryVendor, setInventoryVendor] = useState<InventoryVendor[]>([]);
  const [inventoryAdditional, setInventoryAdditional] = useState<
    AdditionalInfo[]
  >([]);

  const barcodeRef = useRef<TextInput>(null);

  useFocusEffect(
    useCallback(() => {
      setSearch('');
      setCamera(false);
      setTimeout(() => {
        barcodeRef.current?.focus();
      }, 500);
    }, []),
  );

  // Auto focus the barcode input
  useEffect(() => {
    const interval = setInterval(() => {
      if (!camera && !loading) barcodeRef.current?.focus();
    }, 1000);
    return () => clearInterval(interval);
  }, [camera, loading]);

  const codeScanner = useCodeScanner({
    codeTypes: [
      'qr',
      'ean-13',
      'upc-a',
      'ean-8',
      'upc-e',
      'code-128',
      'code-39',
      'code-93',
    ],
    onCodeScanned: codes => {
      if (codes.length > 0 && !isAlertVisible) {
        ReadBarcode(codes[0].value);
      }
    },
  });

  const showAlert = (Barcode: string) => {
    setIsAlertVisible(true);
    Alert.alert(
      'Item Not Found',
      'Would you like to add this item?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => {
            setIsAlertVisible(false);
            setCamera(false);
            setSearch('');
            barcodeRef?.current?.focus();
          },
        },
        {
          text: 'Add Item',
          onPress: () => {
            navigation.navigate('ItemType', {
              ItemData: Barcode,
              ISCREATE: true,
            });
            setIsAlertVisible(false);
          },
        },
      ],
      {cancelable: false},
    );
  };

  const ReadBarcode = async (scanValue: string) => {
    if (!scanValue || loading) return;

    setLoading(true);
    setSearch(scanValue);

    try {
      const getBarcode = await GetItemsParamsNoFilterNoReturn(
        (await getInventoryPort()).toString(),
        '/inventory/:ItemNum',
        {ItemNum: scanValue},
      );

      if (Array.isArray(getBarcode) && getBarcode.length === 0) {
        setLoading(false);
        showAlert(scanValue);
      } else {
        const vendorItems = await GetItemsParamsNoFilter(
          (await getInventoryPort()).toString(),
          '/getvendoritemsbyItemNum/:Vendor_Number/:ItemNum',
          setInventoryVendor,
          {
            Vendor_Number: getBarcode[0]?.Vendor_Number,
            ItemNum: getBarcode[0]?.ItemNum,
          },
          false,
        );

        const inventoryAdditional = await GetItemsParamsNoFilter(
          (await getInventoryPort()).toString(),
          '/getInventoryAdditional/:ItemNum',
          setInventoryAdditional,
          {
            ItemNum: getBarcode[0]?.ItemNum,
          },
          false,
        );

        setLoading(false);

        if (getBarcode[0].ItemType === 3) {
          navigation.navigate('ChoiceItem', {
            ItemData: getBarcode,
            IsPickList: true,
            ISCREATE: true,
          });
        } else {
          const lotteryDepartment = await AsyncStorage.getItem(
            'LOTTERY_DEP_ID',
          );

          if (lotteryDepartment) {
            if (lotteryDepartment === getBarcode[0]?.Dept_ID) {
              navigation.navigate('Barcode', {
                ItemData: getBarcode,
                VENDORITEM: vendorItems,
                ADDITIONAL: inventoryAdditional,
                ISCREATE: true,
                CANEDIT: false,
              });
            } else {
              navigation.navigate('Barcode', {
                ItemData: getBarcode,
                VENDORITEM: vendorItems,
                ADDITIONAL: inventoryAdditional,
                ISCREATE: true,
                CANEDIT: true,
              });
            }
          } else {
            navigation.navigate('Barcode', {
              ItemData: getBarcode,
              VENDORITEM: vendorItems,
              ADDITIONAL: inventoryAdditional,
              ISCREATE: true,
              CANEDIT: true,
            });
          }
        }
      }
    } catch (error) {
      console.log('Error reading barcode:', error);
      setLoading(false);
    }
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: wp('4%'),
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: hp('2%'),
      marginBottom: hp('2%'),
    },
    headerTitleContainer: {
      flex: 1,
      alignItems: 'center',
    },
    headerTitle: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.xLarge,
    },
    cameraButton: {
      width: hp('5%'),
      height: hp('5%'),
      borderRadius: hp('2.5%'),
      justifyContent: 'center',
      alignItems: 'center',
    },
    hiddenSearch: {
      position: 'absolute',
      opacity: 0,
      width: 100,
      height: 50,
    },
    contentContainer: {
      flex: 5,
      justifyContent: 'center',
      alignItems: 'center',
    },
    scannerContainer: {
      width: '100%',
      alignItems: 'center',
    },
    scannerFrame: {
      width: wp('70%'),
      height: wp('70%'),
      borderWidth: 2,
      borderRadius: 20,
      borderStyle: 'dashed',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: hp('4%'),
    },
    instructionsContainer: {
      alignItems: 'center',
      paddingHorizontal: wp('5%'),
    },
    instructionsText: {
      fontFamily: Fonts.OnestMedium,
      fontSize: hp('2%'),
      textAlign: 'center',
      lineHeight: hp('3%'),
      marginBottom: hp('3%'),
    },
    scanNowButton: {
      paddingVertical: 16,
      paddingHorizontal: 24,
      borderRadius: 30,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: isDark ? 0.4 : 0.2,
      shadowRadius: 4,
      elevation: 4,
    },
    scanNowButtonText: {
      fontFamily: Fonts.OnestMedium,
      fontSize: FontSizes.large,
      color: '#FFFFFF',
    },
    loadingContainer: {
      alignItems: 'center',
      marginTop: hp('2%'),
    },
    loadingText: {
      fontFamily: Fonts.OnestMedium,
      fontSize: hp('2%'),
      marginTop: hp('2%'),
    },
    modalContainer: {
      flex: 1,
      backgroundColor: '#000',
    },
  });

  return (
    <View style={[styles.container, {backgroundColor: colors.background}]}>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background}
      />

      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerTitleContainer}>
          <Text style={[styles.headerTitle, {color: colors.text}]}>
            Barcode Scanner
          </Text>
        </View>
      </View>

      {/* Hidden Search Input (functional but invisible) */}
      <View style={styles.hiddenSearch}>
        <SearchManual
          textInputRef={barcodeRef}
          PlaceHolder="Scan barcode..."
          Value={search}
          onChangeText={value => ReadBarcode(value)}
        />
      </View>

      {/* Main Content */}
      <View style={styles.contentContainer}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={[styles.loadingText, {color: colors.primary}]}>
              Processing barcode...
            </Text>
          </View>
        ) : (
          <View style={styles.scannerContainer}>
            <View style={[styles.scannerFrame, {borderColor: colors.border}]}>
              <MaterialCommunityIcons
                name="barcode-scan"
                size={hp('12%')}
                color={colors.textSecondary}
              />
            </View>

            <View style={styles.instructionsContainer}>
              <Text
                style={[
                  styles.instructionsText,
                  {color: colors.textSecondary},
                ]}>
                Hold device steady, aim at barcode,{'\n'}
                and press the camera button Below
              </Text>
              <TouchableOpacity
                style={[
                  styles.scanNowButton,
                  {
                    backgroundColor: colors.primary,
                    shadowColor: colors.shadow,
                  },
                ]}
                onPress={() => setCamera(true)}>
                <FontAwesome5Icon name="camera" size={20} color="#FFFFFF" />
                <Text style={styles.scanNowButtonText}>Camera Scan</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>

      {/* Scanner Camera Modal */}
      <View style={{flex: 1}}>
        <Modal
          animationType="fade"
          transparent={false}
          visible={camera}
          onRequestClose={() => setCamera(false)}>
          <AppScanner
            codeScanner={codeScanner}
            onClose={() => {
              setCamera(false);
              barcodeRef?.current?.focus();
            }}
          />
        </Modal>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: MaterialColors.background,
    paddingHorizontal: wp('4%'),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: hp('2%'),
    marginBottom: hp('2%'),
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontFamily: Fonts.OnestBold,
    fontSize: FontSizes.xLarge,
    color: MaterialColors.text.primary,
  },
  cameraButton: {
    backgroundColor: MaterialColors.primary.main,
    width: hp('5%'),
    height: hp('5%'),
    borderRadius: hp('2.5%'),
    justifyContent: 'center',
    alignItems: 'center',
  },
  hiddenSearch: {
    position: 'absolute',
    opacity: 0,
    width: 100,
    height: 50,
  },
  contentContainer: {
    flex: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scannerContainer: {
    width: '100%',
    alignItems: 'center',
  },
  scannerFrame: {
    width: wp('70%'),
    height: wp('70%'),
    borderWidth: 2,
    borderColor: MaterialColors.grey[200],
    borderRadius: 20,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: hp('4%'),
  },
  instructionsContainer: {
    alignItems: 'center',
    paddingHorizontal: wp('5%'),
  },
  instructionsText: {
    fontFamily: Fonts.OnestMedium,
    fontSize: hp('2%'),
    color: MaterialColors.text.secondary,
    textAlign: 'center',
    lineHeight: hp('3%'),
    marginBottom: hp('3%'),
  },
  scanNowButton: {
    backgroundColor: MaterialColors.primary.main,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 30,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  scanNowButtonText: {
    fontFamily: Fonts.OnestMedium,
    fontSize: FontSizes.large,
    color: MaterialColors.surface,
  },
  loadingContainer: {
    alignItems: 'center',
    marginTop: hp('2%'),
  },
  loadingText: {
    fontFamily: Fonts.OnestMedium,
    fontSize: hp('2%'),
    color: MaterialColors.primary.main,
    marginTop: hp('2%'),
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#000',
  },
});

export default Scanner;
