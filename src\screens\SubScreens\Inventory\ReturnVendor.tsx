import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  Keyboard,
  ActivityIndicator,
  FlatList,
  Platform,
} from 'react-native';
import React, {memo, useCallback, useEffect, useRef, useState} from 'react';
import Header from '../../../components/Inventory/Header';
import {
  BrandOrSubCategory,
  Brands,
  Department,
  Inventory,
  Inventory_In,
  PurchaseOrder,
  PurchaseOrderItems,
  SubCategories,
  UpdatePurchaseOrder,
  Vendor,
  VendorItem,
} from '../../../server/types';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {
  createData,
  GetAllItems,
  GetAllItemsWithFilter,
  getFormateDate,
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
  GetItemsWithParams,
  handleSearch,
  onSearchChange_Common,
  onSearchChange_CommonVendor,
  showAlert,
  showAlertOK,
  updateData,
} from '../../../utils/PublicHelper';
import {getInventoryPort} from '../../../server/InstanceTypes';
import DataList from '../../../components/Inventory/AppList';
import PurchaseOrderItemCart from '../../../components/Inventory/PurchaseOrderItemCart';
import AppButton from '../../../components/Inventory/AppButton';
import {MaterialColors} from '../../../constants/MaterialColors';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {deleteItem} from '../../../server/service';
import {
  applyDefaults,
  applyDefaultsInventoryAdjust,
  applyDefaultsUpdatePurchaseOrder,
} from '../../../Validator/Inventory/Barcode';
import AppFocus from '../../../components/Inventory/AppFocus';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../../styles/fonts';
import Search from '../../../components/Inventory/Search';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AppFilter from '../../../components/Inventory/AppFilter';
import AppSearchWIthFilter from '../../../components/Inventory/AppSearchWIthFilter';
import AntDesign from 'react-native-vector-icons/AntDesign';
import CustomCheckbox from '../../../components/Inventory/CustomCheckbox';
import FAB from '../../../components/common/FAB';
import AppScanner from '../../../components/Inventory/AppScanner';
import {useCodeScanner} from 'react-native-vision-camera';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

type CountItemRouteProp = RouteProp<any, 'ReturnToVendor'>;

const ReturnVendor: React.FC<{
  route: CountItemRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [mainPO, setMainPO] = useState<PurchaseOrder>(route?.params?.ItemData);
  const [itemPO, setItemPO] = useState<PurchaseOrderItems[]>([]);
  const [initialPO, setInitialPO] = useState<PurchaseOrderItems[]>([]);
  const [itemPOFilter, setItemPOFilter] = useState<PurchaseOrderItems[]>([]);
  const [vendorItems, setVendorItems] = useState<VendorItem[]>([]);
  const [vendorItemsFilter, setVendorItemsFilter] = useState<VendorItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [adjust, setAdjust] = useState<Inventory>();
  const [filter, setFilter] = useState<boolean>(false);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [selectedVendor, setSelectedVendor] = useState<string>('');
  const [selectedBrand, setSelectedBrand] = useState<string>('');
  const [selectedSubCategory, setSelectedSubCategory] = useState<string>('');

  const [brandorSubCat, SetBrandSubOrCat] = useState<BrandOrSubCategory[]>([]);

  const [getBrands, setGetBrands] = useState<Brands[]>([]);
  const [getSubCategories, setGetSubCategories] = useState<SubCategories[]>([]);

  const [isEnableFilter, setIsEnableFilter] = useState<boolean>(false);
  const [showLookup, setshowLookup] = useState<boolean>(false);
  const [isCreated, setIsCreated] = useState<boolean>(false);
  useFocusEffect(
    useCallback(() => {
      getIntialDetals();
      setModalVisible(false);
      setshowLookup(false);
      setSearchQuery('');
      setTimeout(() => {
        if (textInputRef.current) {
          textInputRef.current.blur();
          setTimeout(() => {
            if (textInputRef.current) {
              textInputRef.current.focus();
            }
          }, 50);
        }
      }, 50);
    }, []),
  );

  const getIntialDetals = async () => {
    GetItemsWithParams(
      (await getInventoryPort()).toString(),
      '/getpurchaseorderitems/:PO_Number',
      setItemPO,
      setItemPOFilter,
      setLoading,
      {PO_Number: route?.params?.ItemData?.PO_Number},
    );
    GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/getpurchaseorderitems/:PO_Number',
      setInitialPO,
      {PO_Number: route?.params?.ItemData?.PO_Number},
      false,
    );
    // if (route.params?.POTYPE === 1) {
    //   GetItemsParamsNoFilter(
    //     (await getInventoryPort()).toString(),
    //     '/getvendoritems/:Vendor_Number',
    //     setVendorItems,
    //     {
    //       Vendor_Number:
    //         route?.params?.ItemData?.Vendor_Number || mainPO.Vendor_Number,
    //     },
    //   );

    //   GetItemsParamsNoFilter(
    //     (await getInventoryPort()).toString(),
    //     '/getvendoritems/:Vendor_Number',
    //     setVendorItemsFilter,
    //     {
    //       Vendor_Number:
    //         route?.params?.ItemData?.Vendor_Number || mainPO.Vendor_Number,
    //     },
    //   );
    // } else {
    GetAllItemsWithFilter(
      (await getInventoryPort()).toString(),
      '/getAllvendoritems',
      setVendorItems,
      setVendorItemsFilter,
      setLoading,
      false,
    );
    // }
    GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/getpounique/:PO_Number',
      setMainPO,
      {PO_Number: route?.params?.ItemData?.PO_Number || mainPO.PO_Number},
      true,
    );
  };

  const renderItem = ({item}: {item: PurchaseOrderItems}) => {
    return (
      <View>
        <PurchaseOrderItemCart
          Name={item?.ItemName}
          Ordered={item?.Quan_Ordered}
          RecivedNow={item?.Quan_Received}
          DamagedNow={item.Quan_Damaged}
          Delete={() => DeletePurchaseItem(item?.PO_Number, item.ItemNum, item)}
          ItemCost={Number(item.Quan_Ordered) * Number(item.CostPer)}
        />
      </View>
    );
  };

  const DeletePurchaseItem = (
    ponumber: number,
    itemnum: string,
    item: PurchaseOrderItems,
  ) => {
    if (route.params?.POTYPE === 1) {
      removeListItem(ponumber, itemnum, item);
    } else {
      removeDirectPurchase(ponumber, itemnum, item);
    }
  };

  const removeDirectPurchase = (
    PONumber: number,
    Barcode: string,
    PoItems: PurchaseOrderItems,
  ) => {
    showAlert('Are you sure you want to delete?')
      .then(async result => {
        if (result) {
          if (initialPO.length === 1) {
            const result = await deleteItem(
              (await getInventoryPort()).toString(),
              '/DeletePOItems/:PO_Number/:ItemNum',
              {PO_Number: PONumber, ItemNum: Barcode},
            );

            if (result.success) {
              const updatedArray = itemPO.filter(
                item => item.ItemNum !== Barcode,
              );
              setItemPO(updatedArray);
              updatePO(0, 0);
              GetItemsParamsNoFilter(
                (await getInventoryPort()).toString(),
                '/getpounique/:PO_Number',
                setMainPO,
                {
                  PO_Number:
                    route?.params?.ItemData?.PO_Number || mainPO.PO_Number,
                },
                true,
              );
            }
          } else {
            const result = await deleteItem(
              (await getInventoryPort()).toString(),
              '/DeletePOItems/:PO_Number/:ItemNum',
              {PO_Number: PONumber, ItemNum: Barcode},
            );
            if (result.success) {
              const updatedArray = itemPO.filter(
                item => item.ItemNum !== Barcode,
              );
              setItemPO(updatedArray);
              const TotalCostCal =
                Number(PoItems.Quan_Ordered) * Number(PoItems.CostPer);
              const TotalCostValid =
                Number(mainPO.Total_Cost) - Number(TotalCostCal);
              const TotalCost = mainPO.Total_Cost <= 0 ? 0 : TotalCostValid;

              const ExpectedCal =
                Number(mainPO.ExpectedAmountToReceive) -
                Number(PoItems.Quan_Ordered);
              const ExpectedRecive =
                mainPO.ExpectedAmountToReceive <= 0 ? 0 : ExpectedCal;
              updatePO(TotalCost, ExpectedRecive);
              GetItemsParamsNoFilter(
                (await getInventoryPort()).toString(),
                '/getpounique/:PO_Number',
                setMainPO,
                {
                  PO_Number:
                    route?.params?.ItemData?.PO_Number || mainPO.PO_Number,
                },
                true,
              );
            }
          }
        }
      })
      .catch(error => {
        console.error('Error showing alert', error);
      });
  };
  const removeListItem = (
    PONumber: number,
    Barcode: string,
    PoItems: PurchaseOrderItems,
  ) => {
    showAlert('Are you sure you want to delete?')
      .then(async result => {
        if (result) {
          let TotalCost = 0;
          let ExpectedRecive = 0;
          const positiveTotal = Math.abs(mainPO.Total_Cost);

          const calQuanOrdered = Math.abs(PoItems.Quan_Ordered);
          const calTotals = Number(calQuanOrdered) * Number(PoItems.CostPer);

          const TotalCostCal = Number(positiveTotal) - Number(calTotals);

          const positiveExpected = Math.abs(mainPO.ExpectedAmountToReceive);

          const ExpectedReciveCal =
            Number(positiveExpected) - Number(calQuanOrdered);

          if (TotalCostCal > 0) {
            const TotalCostIN = '-' + TotalCostCal;

            const TotalExpectIN = '-' + ExpectedReciveCal;

            TotalCost = Number(TotalCostIN);

            ExpectedRecive = Number(TotalExpectIN);
          } else {
            const TotalCostIN = TotalCostCal;

            const TotalExpectIN = ExpectedReciveCal;

            TotalCost = Number(TotalCostIN);

            ExpectedRecive = Number(TotalExpectIN);
          }

          const result = await deleteItem(
            (await getInventoryPort()).toString(),
            '/DeletePOItems/:PO_Number/:ItemNum',
            {PO_Number: PONumber, ItemNum: Barcode},
          );
          if (result.success) {
            const updatedArray = itemPO.filter(
              item => item.ItemNum !== Barcode,
            );
            setItemPO(updatedArray);
            updatePO(TotalCost, ExpectedRecive);
            GetItemsParamsNoFilter(
              (await getInventoryPort()).toString(),
              '/getpounique/:PO_Number',
              setMainPO,
              {
                PO_Number:
                  route?.params?.ItemData?.PO_Number || mainPO.PO_Number,
              },
              true,
            );
          }
        } else {
          console.log('Item will not be deleted');
        }
      })
      .catch(error => {
        console.error('Error showing alert', error);
      });
  };

  const updatePO = async (TotalCost?: number, Expected?: number) => {
    const poItmes: Partial<UpdatePurchaseOrder> = {
      PO_Number: mainPO.PO_Number,
      Total_Cost: TotalCost,
      Total_Cost_Received: route.params?.POTYPE === 1 ? TotalCost : 0,
      ExpectedAmountToReceive: Expected,
      Store_ID: mainPO.Store_ID,
      DateTime: mainPO.DateTime,
      Reference: mainPO.Reference,
      Vendor_Number: mainPO.Vendor_Number,
      Ship_Via: mainPO.Ship_Via,
      Status: 'C',
      Cashier_ID: mainPO.Cashier_ID,
      Due_Date: mainPO.Due_Date,
      Last_Modified: mainPO.Last_Modified,
      Cancel_Date: mainPO.DateTime,
      Order_Reason: mainPO.Order_Reason,
      POType: route.params?.POTYPE === 1 ? 1 : 2,
      Dirty: true,
      Print_Notes_On_PO: true,
      ShipTo_1: mainPO.ShipTo_1,
      ShipTo_2: mainPO.ShipTo_2,
      ShipTo_4: mainPO.ShipTo_4,
    };

    const applyDefault = applyDefaultsUpdatePurchaseOrder(poItmes);

    const result = await updateData<UpdatePurchaseOrder>({
      baseURL: (await getInventoryPort()).toString(),
      data: applyDefault,
      endpoint: '/updatePoSummary',
    });
    if (result) {
      GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/getpounique/:PO_Number',
        setMainPO,
        {PO_Number: route?.params?.ItemData?.PO_Number || mainPO.PO_Number},
        true,
      );
    } else {
      console.log('Error!!');
    }
  };

  const isItemNumExist = (itemNum: string) => {
    return itemPO.some(item => item.ItemNum === itemNum);
  };
  const VendorItemAdd = (item: VendorItem) => {
    const IsExists = isItemNumExist(item.ItemNum);
    if (IsExists) {
      Alert.alert('Item Already Exists');
    } else {
      setModalVisible(false);
      setshowLookup(false);
      setSearchQuery('');
      if (route?.params?.POTYPE === 1) {
        navigation.navigate('AddVendorItemQuanity', {
          ItemData: item,
          Main: mainPO,
        });
      } else {
        navigation.navigate('PurchaseOrderQuanity', {
          ItemData: item,
          Main: mainPO,
          IsDirect: true,
        });
      }
    }
  };

  // const renderModalItem = ({item}: {item: VendorItem}) => {
  //   return (
  //     <TouchableOpacity
  //       style={styles.modalItemCard}
  //       onPress={() => VendorItemAdd(item)}>
  //       <View style={styles.itemInfo}>
  //         <Text numberOfLines={1} ellipsizeMode="tail" style={styles.itemName}>
  //           {item.ItemName}
  //         </Text>
  //         <Text style={styles.itemDetail}>
  //           {`Per Case: ${item.NumPerVenCase}`}
  //         </Text>
  //       </View>
  //       <Text style={styles.itemPrice}>${item.CostPer.toFixed(2)}</Text>
  //     </TouchableOpacity>
  //   );
  // };

  const onSearchChange = (text: string) => {
    setSearchQuery(text);
    setPage(1);
    handleSearch(
      text,
      vendorItems,
      ['ItemName', 'ItemNum'],
      setVendorItemsFilter,
      setLoading,
    );
  };

  const handleLoadMore = () => {
    if (!loading) {
      setPage(prevPage => prevPage + 1);
    }
  };

  const ScanBarcode = (Barcode: string) => {
    const IsExists = isItemNumExist(Barcode);
    if (IsExists) {
      Alert.alert('Item Already Exists');
    } else {
      const GetVendorItem = vendorItems.filter(
        item => item.ItemNum === Barcode,
      );
      VendorItemAdd(GetVendorItem[0]);
    }
  };
  const getAllVendorItems = async () => {
    GetAllItemsWithFilter(
      (await getInventoryPort()).toString(),
      '/getvendoritemsAll',
      setVendorItems,
      setVendorItemsFilter,
      setLoading,
      false,
    );
  };

  const AdjustStocking = () => {
    if (itemPO.length > 0) {
      if (route.params?.POTYPE === 1) {
        itemPO.map((quan: PurchaseOrderItems) => {
          AdjustInventory_Return(quan);
        });
      } else {
        itemPO.map((quan: PurchaseOrderItems) => {
          AdjustInventory_Direct(quan);
        });
      }
    } else {
      Alert.alert('Please Select Direct Purchase Items');
    }
  };

  const AdjustInventory_Direct = async (PurchaseItems: PurchaseOrderItems) => {
    const getInventory = await GetItemsParamsNoFilterNoReturn(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      {ItemNum: PurchaseItems.ItemNum},
    );

    const adjustStock =
      Number(PurchaseItems.Quan_Ordered) + Number(getInventory[0].In_Stock);
    const inventoryData: Partial<Inventory> = {
      ItemNum: getInventory[0]?.ItemNum,
      ItemName: getInventory[0]?.ItemName,
      Dept_ID: getInventory[0]?.Dept_ID,
      Cost: getInventory[0]?.Cost,
      Price: getInventory[0]?.Price,
      Retail_Price: getInventory[0]?.Retail_Price,
      In_Stock: adjustStock,
      Date_Created: getInventory[0]?.Date_Created,
      Last_Sold: getInventory[0]?.Last_Sold,
      Location: getInventory[0]?.Location,
      Vendor_Number: getInventory[0]?.Vendor_Number,
      Vendor_Part_Num: getInventory[0]?.Vendor_Part_Num,
      Reorder_Level: getInventory[0]?.Reorder_Level,
      Reorder_Quantity: getInventory[0]?.Reorder_Quantity,
      ReOrder_Cost: getInventory[0]?.ReOrder_Cost,
      Unit_Size: getInventory[0]?.Unit_Size,
      Unit_Type: getInventory[0]?.Unit_Type,
      FoodStampable: getInventory[0]?.FoodStampable,
      Tax_1: getInventory[0]?.Tax_1[0],
      Tax_2: getInventory[0]?.Tax_2[0],
      Tax_3: getInventory[0]?.Tax_3[0],
      Tax_4: getInventory[0]?.Tax_4[0],
      Tax_5: getInventory[0]?.Tax_5[0],
      Tax_6: getInventory[0]?.Tax_6[0],
      Check_ID: getInventory[0]?.Check_ID,
      Check_ID2: getInventory[0]?.Check_ID2,
      Store_ID: getInventory[0]?.Store_ID,
      ItemName_Extra: getInventory[0]?.ItemName_Extra,
    };
    const applyDefault = applyDefaults(inventoryData);

    const result = await updateData<Inventory>({
      baseURL: (await getInventoryPort()).toString(),
      data: applyDefault,
      endpoint: '/updatebarcode',
    });

    if (result) {
      const storeId = await AsyncStorage.getItem('STOREID');
      const ValidStore = storeId === null ? '1001' : storeId;
      const CashierID = await AsyncStorage.getItem('SWIPEID');
      const ValideCashier = CashierID === null ? '100101' : CashierID;

      const inventoryAdjustData: Partial<Inventory_In> = {
        ItemNum: getInventory[0].ItemNum,
        Store_ID: ValidStore,
        Quantity: adjustStock,
        DateTime: getFormateDate(Date()),
        Dirty: true,
        TransType: 'C',
        Description: 'ITEM CREATION',
        Cashier_ID: ValideCashier,
        CostPer: getInventory[0].Cost,
      };

      const applyDefault = applyDefaultsInventoryAdjust(inventoryAdjustData);
      const invenIN = await createData<Inventory_In>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefault,
        endpoint: '/createinvetoryin',
      });
      if (invenIN) {
        Alert.alert('Direct Purchase Items Created');
        navigation.navigate('More');
      } else {
        Alert.alert('Failed to Add Item');
      }
    }
  };

  const AdjustInventory_Return = async (PurchaseItems: PurchaseOrderItems) => {
    const getInventory = await GetItemsParamsNoFilterNoReturn(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      {ItemNum: PurchaseItems.ItemNum},
    );
    const adjustStock =
      Number(getInventory[0]?.In_Stock) + Number(PurchaseItems.Quan_Ordered);
    const inventoryData: Partial<Inventory> = {
      ItemNum: getInventory[0]?.ItemNum,
      ItemName: getInventory[0]?.ItemName,
      Dept_ID: getInventory[0]?.Dept_ID,
      Cost: getInventory[0]?.Cost,
      Price: getInventory[0]?.Price,
      Retail_Price: getInventory[0]?.Retail_Price,
      In_Stock: adjustStock,
      Date_Created: getInventory[0]?.Date_Created,
      Last_Sold: getInventory[0]?.Last_Sold,
      Location: getInventory[0]?.Location,
      Vendor_Number: getInventory[0]?.Vendor_Number,
      Vendor_Part_Num: getInventory[0]?.Vendor_Part_Num,
      Reorder_Level: getInventory[0]?.Reorder_Level,
      Reorder_Quantity: getInventory[0]?.Reorder_Quantity,
      ReOrder_Cost: getInventory[0]?.ReOrder_Cost,
      Unit_Size: getInventory[0]?.Unit_Size,
      Unit_Type: getInventory[0]?.Unit_Type,
      FoodStampable: getInventory[0]?.FoodStampable,
      Tax_1: getInventory[0]?.Tax_1[0],
      Tax_2: getInventory[0]?.Tax_2[0],
      Tax_3: getInventory[0]?.Tax_3[0],
      Tax_4: getInventory[0]?.Tax_4[0],
      Tax_5: getInventory[0]?.Tax_5[0],
      Tax_6: getInventory[0]?.Tax_6[0],
      Check_ID: getInventory[0]?.Check_ID,
      Check_ID2: getInventory[0]?.Check_ID2,
      Store_ID: getInventory[0]?.Store_ID,
      ItemName_Extra: getInventory[0]?.ItemName_Extra,
    };
    const applyDefault = applyDefaults(inventoryData);

    const result = await updateData<Inventory>({
      baseURL: (await getInventoryPort()).toString(),
      data: applyDefault,
      endpoint: '/updatebarcode',
    });

    if (result) {
      const storeId = await AsyncStorage.getItem('STOREID');
      const ValidStore = storeId === null ? '1001' : storeId;
      const CashierID = await AsyncStorage.getItem('SWIPEID');
      const ValideCashier = CashierID === null ? '100101' : CashierID;

      const inventoryAdjustData: Partial<Inventory_In> = {
        ItemNum: getInventory[0]?.ItemNum,
        Store_ID: ValidStore,
        Quantity: '-' + adjustStock,
        DateTime: getFormateDate(Date()),
        Dirty: true,
        TransType: 'C',
        Description: 'ITEM CREATION',
        Cashier_ID: ValideCashier,
        CostPer: getInventory[0]?.Cost,
      };

      const applyDefault = applyDefaultsInventoryAdjust(inventoryAdjustData);
      const invenIN = await createData<Inventory_In>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefault,
        endpoint: '/createinvetoryin',
      });
      if (invenIN) {
        Alert.alert('Vendor Return Created');
        navigation.navigate('More');
      } else {
        Alert.alert('Failed to Add Item');
      }
    }
  };
  const textInputRef = useRef<TextInput>(null);
  const toggleLookup = useCallback(async (value: boolean) => {
    setSearchQuery('');
    if (route.params?.POTYPE === 1) {
      GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/getvendoritems/:Vendor_Number',
        setVendorItems,
        {
          Vendor_Number:
            route?.params?.ItemData?.Vendor_Number || mainPO.Vendor_Number,
        },
      );

      GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/getvendoritems/:Vendor_Number',
        setVendorItemsFilter,
        {
          Vendor_Number:
            route?.params?.ItemData?.Vendor_Number || mainPO.Vendor_Number,
        },
      );
    } else {
      GetAllItemsWithFilter(
        (await getInventoryPort()).toString(),
        '/getAllvendoritems',
        setVendorItems,
        setVendorItemsFilter,
        setLoading,
        false,
      );
    }
    setshowLookup(value);

    if (Platform.OS === 'android') {
      if (value) {
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.blur();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 50);
          }
        }, 50);
      } else {
        Keyboard.dismiss();
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.blur();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 50);
          }
        }, 50);
      }
      return;
    }

    // iOS handling
    if (value) {
      setTimeout(() => {
        textInputRef.current?.focus();
      }, 100);
    } else {
      Keyboard.dismiss();
    }
  }, []);

  const NavHandle = () => {
    if (itemPO.length > 0) {
      showAlert(
        route.params?.POTYPE === 1
          ? 'Vendor Return Items Not Saved Yet, Would You Like to Save Close?'
          : 'Direct Purchase Items Not Saved Yet, Would You Like to Save Close?',
      )
        .then(async result => {
          if (result) {
            AdjustStocking();
            navigation.navigate('More');
          }
        })
        .catch(error => {
          console.error('Error showing alert', error);
        });
    } else {
      showAlert(
        `You didn't add any items to this direct purchase order!`,
        'Confirmation',
        true,
        'OK',
        'Discard Order',
      )
        .then(async result => {
          if (result) {
            navigation.navigate('More');
          }
        })
        .catch(error => {
          console.error('Error showing alert', error);
        });
    }
  };

  const renderModalItem = useCallback(
    ({item}) => <VendorItemComponent item={item} onPress={VendorItemAdd} />,
    [VendorItemAdd],
  );

  // Add these functions inside the component:

  // Optimized key extractor
  const keyExtractor = useCallback(
    (item: VendorItem) => item.ItemNum.toString(),
    [],
  );

  // GetItemLayout for optimized rendering
  const getItemLayout = useCallback(
    (_, index) => ({
      length: 70, // approximate height of each item based on your styles
      offset: 70 * index,
      index,
    }),
    [],
  );

  const handleDoneClick = async () => {
    if (route.params?.POTYPE === 1) {
      GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/getvendoritems/:Vendor_Number',
        setVendorItems,
        {
          Vendor_Number:
            route?.params?.ItemData?.Vendor_Number || mainPO.Vendor_Number,
        },
      );

      GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/getvendoritems/:Vendor_Number',
        setVendorItemsFilter,
        {
          Vendor_Number:
            route?.params?.ItemData?.Vendor_Number || mainPO.Vendor_Number,
        },
      );
    } else {
      GetAllItemsWithFilter(
        (await getInventoryPort()).toString(),
        '/getAllvendoritems',
        setVendorItems,
        setVendorItemsFilter,
        setLoading,
        false,
      );
    }
    setSearchQuery('');
    setshowLookup(false);
    Keyboard.dismiss();
    setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.blur();
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.focus();
          }
        }, 50);
      }
    }, 50);
  };

  // const handleSearchChange = async (text: string) => {
  //   setSearchQuery(text);
  //   const ScanStatus = await onSearchChange_CommonVendor(
  //     text,
  //     vendorItems,
  //     setVendorItemsFilter,
  //     setLoading,
  //     setSearchQuery,
  //     navigation,
  //     showLookup,
  //     mainPO,
  //     isReturnOrDsd,
  //     mainPO.Vendor_Number,
  //     route.params?.POTYPE,
  //   );

  //   if (!showLookup) {
  //     if (!ScanStatus) {
  //       if (textInputRef.current) {
  //         textInputRef.current.clear();
  //         textInputRef.current.blur();
  //       }

  //       setSearchQuery('');
  //       setshowLookup(false);
  //       Keyboard.dismiss();
  //       setTimeout(() => {
  //         if (textInputRef.current) {
  //           textInputRef.current.focus();
  //         }
  //       }, 200);
  //     }
  //   }
  // };

  const handleSearchChange = async (text: string) => {
    setCamera(false);
    const getBarcode = await GetItemsParamsNoFilterNoReturn<VendorItem>(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      {ItemNum: text},
    );
    setSearchQuery(getBarcode[0]?.ItemNum || text);
    if (text) {
      if (showLookup) {
        handleSearch(
          text,
          vendorItemsFilter,
          ['ItemName', 'ItemNum'],
          setVendorItemsFilter,
          setLoading,
        );
      } else {
        if (Array.isArray(getBarcode) && getBarcode.length === 0) {
          const userConfirmed = await showAlert(
            'Item not found. Do you want to create a new item?',
          );
          if (userConfirmed) {
            navigation.navigate('ItemType', {
              ItemData: text,
              ISPO: true,
              MAINPO: mainPO,
              VENDOR: mainPO.Vendor_Number,
              ISDSD: isReturnOrDsd ? true : false,
              POTYPE: route.params?.POTYPE,
            });
          } else {
            if (textInputRef.current) {
              textInputRef.current.clear();
              textInputRef.current.blur();
            }

            setSearchQuery('');
            setshowLookup(false);
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 200);
          }
        } else {
          const FiterExist = isItemNumExist(
            Array.isArray(getBarcode) && getBarcode.length === 0
              ? text
              : getBarcode[0]?.ItemNum,
          );
          if (FiterExist) {
            showAlertOK(
              `This Item is Already Exists. Please Try Again`,
              'Already Exists',
              'OK',
              () => {
                if (textInputRef.current) {
                  textInputRef.current.clear();
                  textInputRef.current.blur();
                }

                setSearchQuery('');
                setshowLookup(false);
                Keyboard.dismiss();
                setTimeout(() => {
                  if (textInputRef.current) {
                    textInputRef.current.focus();
                  }
                }, 200);
              },
            );
          } else {
            const filterVendor = vendorItems.filter(
              item => item.ItemNum === getBarcode[0]?.ItemNum,
            );
            setModalVisible(false);
            setshowLookup(false);
            setSearchQuery('');
            if (route?.params?.POTYPE === 1) {
              navigation.navigate('AddVendorItemQuanity', {
                ItemData: filterVendor[0],
                Main: mainPO,
              });
            } else {
              navigation.navigate('PurchaseOrderQuanity', {
                ItemData: filterVendor[0],
                Main: mainPO,
                IsDirect: true,
              });
            }
          }
        }
      }
    } else {
      setVendorItemsFilter(vendorItems);
    }
  };

  const [camera, setCamera] = useState(false);
  const codeScanner = useCodeScanner({
    codeTypes: [
      'qr',
      'ean-13',
      'upc-a',
      'ean-8',
      'upc-e',
      'code-128',
      'code-39',
      'code-93',
    ],
    onCodeScanned: codes => {
      if (codes.length > 0) {
        handleSearchChange(codes[0].value);
      }
    },
  });

  const isReturnOrDsd = route.params?.POTYPE === 1 ? false : true;

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const VendorItemComponent = memo(
    ({
      item,
      onPress,
    }: {
      item: VendorItem;
      onPress: (item: VendorItem) => void;
    }) => {
      return (
        <TouchableOpacity
          style={styles.modalItemCard}
          onPress={() => onPress(item)}>
          <View style={styles.itemInfo}>
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              style={styles.itemName}>
              {item.ItemName}
            </Text>
            <Text style={styles.itemDetail}>
              {`Per Case: ${item.NumPerVenCase}`}
            </Text>
          </View>
          <Text style={styles.itemPrice}>${item.CostPer.toFixed(2)}</Text>
        </TouchableOpacity>
      );
    },
    (prevProps, nextProps) => {
      // Only re-render if the item ItemNum changes
      return prevProps.item.ItemNum === nextProps.item.ItemNum;
    },
  );

  // Empty list component
  const EmptyVendorList = memo(({searchQuery}: {searchQuery: string}) => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>
        {searchQuery ? 'No matching items found' : 'No vendor items available'}
      </Text>
    </View>
  ));

  // Footer loading component
  const VendorListFooter = memo(({loading}: {loading: boolean}) => {
    if (!loading) return null;

    return (
      <View style={styles.footer}>
        <ActivityIndicator size="small" color={MaterialColors.primary.main} />
        <Text style={styles.footerText}>Loading more items...</Text>
      </View>
    );
  });

  const styles = StyleSheet.create({
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: hp('10%'),
    },
    emptyText: {
      fontSize: FontSizes.medium,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: 20,
      fontFamily: Fonts.OnestMedium,
    },
    footer: {
      padding: 15,
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    footerText: {
      marginLeft: 10,
      fontFamily: Fonts.OnestMedium,
      color: colors.textSecondary,
    },
    centeredLoading: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: hp('10%'),
    },
    listContent: {
      paddingBottom: hp('2%'),
    },
    rootContainer: {
      backgroundColor: colors.background,
      flex: 1,
      justifyContent: 'space-between',
    },
    mainContainer: {
      paddingHorizontal: wp('2.5%'),
      flex: 1,
    },
    vendorInfoCard: {
      backgroundColor: colors.surface,
      paddingVertical: hp('1.2%'),
      paddingHorizontal: wp('3%'),
      borderRadius: 8,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: hp('1%'),
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    vendorDetails: {
      gap: hp('0.5%'),
    },
    vendorName: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.large,
      color: colors.text,
    },
    vendorNumber: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: colors.textSecondary,
    },
    totalCost: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.large,
      color: colors.primary,
    },
    addItemsButton: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: wp('0.5%'),
      marginVertical: hp('0.5%'),
    },
    addItemsText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.primary,
      paddingVertical: hp('0.5%'),
      paddingHorizontal: wp('1.5%'),
    },
    bottomButtonContainer: {
      position: 'absolute',
      right: 0,
      bottom: 0,
      left: 0,
      backgroundColor: colors.surface,
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('1.5%'),
      borderTopWidth: 1,
      borderTopColor: colors.border,
      elevation: 4,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: -1},
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 2,
    },
    modalContainer: {
      flex: 1,
      backgroundColor: colors.background,
      paddingHorizontal: wp('2.5%'),
    },
    modalHeaderContainer: {
      width: '95%',
    },
    searchContainer: {
      paddingBottom: hp('1%'),
    },
    checkboxContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      alignSelf: 'flex-end',
      marginVertical: hp('1%'),
    },
    manualEntryText: {
      marginLeft: wp('1%'),
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
    },
    manualEntryActive: {
      color: colors.primary,
    },
    manualEntryInactive: {
      color: colors.textSecondary,
    },
    itemCountText: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestBold,
      color: colors.primary,
      paddingVertical: hp('1%'),
    },
    modalItemCard: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('1%'),
      marginVertical: hp('0.3%'),
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      backgroundColor: colors.card,
    },
    itemInfo: {
      gap: hp('0.4%'),
      flex: 1,
    },
    itemName: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: colors.text,
      numberOfLines: 1,
      ellipsizeMode: 'tail',
    },
    itemDetail: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: colors.textSecondary,
    },
    itemPrice: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: colors.primary,
    },
    filterContainer: {
      flex: 1,
    },
  });

  return (
    <View style={styles.rootContainer}>
      <View style={styles.mainContainer}>
        <Header
          NavName={
            route.params?.POTYPE === 1 ? 'Return to Vendor' : 'Direct Purchase'
          }
          isProvid={true}
          Onpress={() => NavHandle()}
        />

        <View style={styles.vendorInfoCard}>
          <View style={styles.vendorDetails}>
            <Text style={styles.vendorName}>{mainPO.Company}</Text>
            <Text style={styles.vendorNumber}>
              Vendor Number: {mainPO.Vendor_Number}
            </Text>
          </View>
          <Text style={styles.totalCost}>
            $
            {mainPO.Total_Cost.toFixed(2).includes('-0.0')
              ? '0.00'
              : mainPO.Total_Cost.toFixed(2)}
          </Text>
        </View>

        <TouchableOpacity
          style={styles.addItemsButton}
          onPress={() => setModalVisible(true)}>
          <Text style={styles.addItemsText}>Add Items</Text>
          <AntDesign
            name="pluscircle"
            size={hp('3.3%')}
            color={colors.primary}
          />
        </TouchableOpacity>

        {false && (
          <AppFocus
            PlaceHolder="Scan Book Number"
            Title="Scan Book Number"
            onChangeText={value => {
              ScanBarcode(value);
            }}
          />
        )}

        <DataList
          data={itemPO}
          renderItem={renderItem}
          loading={loading}
          Hight="70%"
        />
      </View>

      <View style={styles.bottomButtonContainer}>
        <FAB
          label="Save & Close"
          position="bottomRight"
          onPress={() => AdjustStocking()}
        />
      </View>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeaderContainer}>
            <Header
              NavName="Add Items"
              Onpress={() => setModalVisible(false)}
              isProvid={true}
              isOption={true}
              Options={() => {
                if (textInputRef.current) {
                  textInputRef.current.clear();
                  textInputRef.current.blur();
                }

                setSearchQuery('');
                Keyboard.dismiss();
                setTimeout(() => {
                  if (textInputRef.current) {
                    textInputRef.current.focus();
                  }
                }, 200);

                setCamera(!camera);
              }}
            />
          </View>

          <View style={styles.searchContainer}>
            <AppSearchWIthFilter
              OnSearch={handleSearchChange}
              SearchValue={searchQuery}
              OnSearchSet={() => setFilter(true)}
              isEnableFilter={isEnableFilter}
              Keyboardon={showLookup}
              textInputRef={textInputRef}
              onToggleLookup={value => toggleLookup(value)}
              OnSubmitEditing={handleDoneClick}
            />

            <Text style={styles.itemCountText}>
              Total Items: ({vendorItemsFilter.length || 0})
            </Text>
          </View>
          <View
            style={{
              backgroundColor: colors.background,
              borderRadius: 12,
              flex: 1,
            }}>
            {loading ? (
              <View style={styles.centeredLoading}>
                <ActivityIndicator
                  size="large"
                  color={MaterialColors.primary.main}
                />
              </View>
            ) : (
              <FlatList
                data={vendorItemsFilter}
                keyExtractor={keyExtractor}
                renderItem={renderModalItem}
                getItemLayout={getItemLayout}
                initialNumToRender={10}
                maxToRenderPerBatch={10}
                windowSize={10}
                removeClippedSubviews={true}
                updateCellsBatchingPeriod={75}
                onEndReachedThreshold={0.5}
                onEndReached={handleLoadMore}
                ListEmptyComponent={
                  <EmptyVendorList searchQuery={searchQuery} />
                }
                ListFooterComponent={<VendorListFooter loading={loading} />}
                contentContainerStyle={styles.listContent}
                showsVerticalScrollIndicator={false}
                maintainVisibleContentPosition={{
                  minIndexForVisible: 0,
                }}
              />
            )}
          </View>
        </View>
      </Modal>

      <View style={styles.filterContainer}>
        <AppFilter
          isVisible={filter}
          setIsVisble={setFilter}
          Department={text => setSelectedDepartment(text)}
          Vedor={text => setSelectedVendor(text)}
          Brand={text => setSelectedBrand(text)}
          Category={text => setSelectedSubCategory(text)}
          EnableFilter={setIsEnableFilter}
          selectedDepartment={selectedDepartment}
          selectedVendor={selectedVendor}
          selectedBrand={selectedBrand}
          selectedSubCategory={selectedSubCategory}
          isEnableFilter={isEnableFilter}
        />
      </View>

      <Modal
        animationType="fade"
        transparent={false}
        visible={camera}
        onRequestClose={() => setCamera(false)}>
        <AppScanner
          codeScanner={codeScanner}
          onClose={() => {
            setCamera(false);
            textInputRef?.current?.focus();
          }}
        />
      </Modal>
    </View>
  );
};

export default ReturnVendor;
