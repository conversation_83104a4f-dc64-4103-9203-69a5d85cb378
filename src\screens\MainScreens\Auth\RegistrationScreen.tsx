import React, {useState, useRef} from 'react';
import {
  SafeAreaView,
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  Alert,
  ActivityIndicator,
} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useNavigation, CommonActions} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {register} from '../../../services/apiService';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';
import Header from '../../../components/Inventory/Header';

const Icon = (props: {
  name: string;
  size: number;
  color: string;
  style?: any;
}) => <MaterialCommunityIcons {...props} />;

interface FormErrors {
  organizationName?: string;
  ownerName?: string;
  email?: string;
  phone?: string;
  streetAddress?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  password?: string;
  confirmPassword?: string;
  agreeTerms?: string;
}

const RegistrationScreen = () => {
  const navigation = useNavigation<any>();
  const colors = useThemeColors();
  const {isDark} = useTheme();

  const [organizationName, setOrganizationName] = useState('');
  const [ownerName, setOwnerName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [streetAddress, setStreetAddress] = useState('');
  const [city, setCity] = useState('');
  const [state, setState] = useState('');
  const [zipCode, setZipCode] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);
  const [agreeTerms, setAgreeTerms] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});

  // Refs for input fields
  const organizationNameRef = useRef<TextInput>(null);
  const ownerNameRef = useRef<TextInput>(null);
  const emailRef = useRef<TextInput>(null);
  const phoneRef = useRef<TextInput>(null);
  const streetAddressRef = useRef<TextInput>(null);
  const cityRef = useRef<TextInput>(null);
  const stateRef = useRef<TextInput>(null);
  const zipCodeRef = useRef<TextInput>(null);
  const passwordRef = useRef<TextInput>(null);
  const confirmPasswordRef = useRef<TextInput>(null);
  const scrollViewRef = useRef<ScrollView>(null);

  const ownerPermission = {
    StartOrEndShift: true,
    ActivateBook: true,
    CreateNewGame: true,
    OrganizeSlot: true,
    ViewShift: true,
    ResetShift: true,
    CFA_Inven_Add: true,
    CFA_Inven_Edit: true,
    CFA_Vendors_Add: true,
    CFA_Depts_Add: true,
    CFA_Depts_Edit: true,
    CFA_INVEN_VIEW: true,
    CFA_HH_Create_PO: true,
    CFA_HH_DSD: true,
    CFA_HH_Inv_Count: true,
    CFA_HH_PO_Receive: true,
    CFA_HH_Inv_Adjust: true,
    CFA_HH_PRINT_LABELS: true,
  };

  const validateForm = () => {
    let isValid = true;
    let newErrors: FormErrors = {};
    let firstErrorField: string | null = null;

    if (!organizationName.trim()) {
      newErrors.organizationName = 'Organization name is required';
      if (!firstErrorField) {
        firstErrorField = 'organizationName';
      }
      isValid = false;
    }

    if (!ownerName.trim()) {
      newErrors.ownerName = 'Owner name is required';
      if (!firstErrorField) {
        firstErrorField = 'ownerName';
      }
      isValid = false;
    }

    if (!email.trim()) {
      newErrors.email = 'Email is required';
      if (!firstErrorField) {
        firstErrorField = 'email';
      }
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Email is invalid';
      if (!firstErrorField) {
        firstErrorField = 'email';
      }
      isValid = false;
    }

    if (!phone.trim()) {
      newErrors.phone = 'Phone is required';
      if (!firstErrorField) {
        firstErrorField = 'phone';
      }
      isValid = false;
    } else if (phone.replace(/\D/g, '').length < 10) {
      newErrors.phone = 'Phone number must be at least 10 digits';
      if (!firstErrorField) {
        firstErrorField = 'phone';
      }
      isValid = false;
    }

    if (!streetAddress.trim()) {
      newErrors.streetAddress = 'Street address is required';
      if (!firstErrorField) {
        firstErrorField = 'streetAddress';
      }
      isValid = false;
    }

    if (!city.trim()) {
      newErrors.city = 'City is required';
      if (!firstErrorField) {
        firstErrorField = 'city';
      }
      isValid = false;
    }

    if (!state.trim()) {
      newErrors.state = 'State is required';
      if (!firstErrorField) {
        firstErrorField = 'state';
      }
      isValid = false;
    }

    if (!zipCode.trim()) {
      newErrors.zipCode = 'Zip code is required';
      if (!firstErrorField) {
        firstErrorField = 'zipCode';
      }
      isValid = false;
    }

    if (!password) {
      newErrors.password = 'Password is required';
      if (!firstErrorField) {
        firstErrorField = 'password';
      }
      isValid = false;
    } else if (password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
      if (!firstErrorField) {
        firstErrorField = 'password';
      }
      isValid = false;
    }

    if (password !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
      if (!firstErrorField) {
        firstErrorField = 'confirmPassword';
      }
      isValid = false;
    }

    if (!agreeTerms) {
      newErrors.agreeTerms = 'You must agree to the Terms and Conditions';
      // Note: checkbox doesn't get focus, so we don't set firstErrorField for this
      isValid = false;
    }

    setErrors(newErrors);

    // Focus on the first field with error
    if (!isValid && firstErrorField) {
      setTimeout(() => {
        focusField(firstErrorField);
      }, 100);
    }

    return isValid;
  };

  const focusField = (fieldName: string) => {
    const fieldRefs: {[key: string]: React.RefObject<TextInput>} = {
      organizationName: organizationNameRef,
      ownerName: ownerNameRef,
      email: emailRef,
      phone: phoneRef,
      streetAddress: streetAddressRef,
      city: cityRef,
      state: stateRef,
      zipCode: zipCodeRef,
      password: passwordRef,
      confirmPassword: confirmPasswordRef,
    };

    const ref = fieldRefs[fieldName];
    if (ref?.current) {
      // First, measure the position of the field to scroll to it
      ref.current.measureLayout(
        scrollViewRef.current as any,
        (x, y) => {
          // Scroll to the field with some offset to show the label
          scrollViewRef.current?.scrollTo({
            y: Math.max(0, y - 100), // 100px offset to ensure label is visible
            animated: true,
          });

          // Focus the field after scrolling
          setTimeout(() => {
            ref.current?.focus();
          }, 200);
        },
        () => {
          // Fallback if measureLayout fails
          ref.current?.focus();
        },
      );
    }
  };

  const handleRegister = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    const responseData = await register({
      owner_name: ownerName,
      organization_name: organizationName,
      street_address: streetAddress,
      city: city,
      state: state,
      zip_code: zipCode,
      email: email,
      phone: phone,
      password: password,
      subscription_type: 'trial',
    });

    if (responseData.success) {
      await AsyncStorage.setItem('userToken', responseData.data.token);
      await AsyncStorage.setItem(
        'userData',
        JSON.stringify(responseData.data.user),
      );
      await AsyncStorage.setItem(
        'organizationData',
        JSON.stringify(responseData.data.organization),
      );
      await AsyncStorage.setItem(
        'LOTTERY_PERMISSIONS',
        JSON.stringify(ownerPermission),
      );

      await AsyncStorage.setItem('SWIPEID', '01');

      console.log(
        JSON.stringify(responseData.data.organization),
        'Organization Data',
      );

      setIsLoading(false);

      Alert.alert(
        'Registration Successful',
        `2-day free trial activated for ${organizationName}.`,
        [
          {
            text: 'Ok',
            onPress: () =>
              navigation.dispatch(
                CommonActions.reset({
                  index: 0,
                  routes: [
                    {
                      name: 'ConfigureIp',
                      params: {userData: responseData.data.user},
                    },
                  ],
                }),
              ),
          },
        ],
      );
    } else {
      setIsLoading(false);
      Alert.alert('Registration Failed', responseData.error);
    }
  };

  const handleRegisterWithActivation = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    const responseData = await register({
      owner_name: ownerName,
      organization_name: organizationName,
      street_address: streetAddress,
      city: city,
      state: state,
      zip_code: zipCode,
      email: email,
      phone: phone,
      password: password,
      subscription_type: 'trial',
      status: 'requested',
    });

    if (responseData.success) {
      await AsyncStorage.setItem('userToken', responseData.data.token);
      await AsyncStorage.setItem(
        'userData',
        JSON.stringify(responseData.data.user),
      );
      await AsyncStorage.setItem(
        'organizationData',
        JSON.stringify(responseData.data.organization),
      );

      console.log(
        JSON.stringify(responseData.data.organization),
        'Organization Data',
      );

      await AsyncStorage.setItem(
        'LOTTERY_PERMISSIONS',
        JSON.stringify(ownerPermission),
      );

      await AsyncStorage.setItem('SWIPEID', '01');

      setIsLoading(false);

      Alert.alert(
        'Registration Successful',
        'Request for activation key sent to admin.',
        [
          {
            text: 'Ok',
            onPress: () =>
              navigation.dispatch(
                CommonActions.reset({
                  index: 0,
                  routes: [
                    {
                      name: 'ConfigureIp',
                      params: {userData: responseData.data.user},
                    },
                  ],
                }),
              ),
          },
        ],
      );
    } else {
      setIsLoading(false);
      Alert.alert('Registration Failed', responseData.error);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    contentContainer: {
      flex: 1,
      padding: 24,
    },
    formTitle: {
      fontSize: 24,
      color: colors.text,
      fontWeight: 'bold',
      marginBottom: 24,
      marginTop: 16,
      textAlign: 'left',
    },
    formContainer: {
      width: '100%',
    },
    labelContainer: {
      marginBottom: 6,
    },
    inputLabel: {
      fontSize: 14,
      color: colors.text,
      fontWeight: '500',
    },
    inputContainer: {
      marginBottom: 16,
      position: 'relative',
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 12,
      backgroundColor: colors.card,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: isDark ? 0.3 : 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    input: {
      fontSize: 16,
      paddingVertical: 14,
      paddingHorizontal: 16,
      color: colors.text,
    },
    eyeIcon: {
      position: 'absolute',
      right: 16,
      top: 14,
    },
    checkboxContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 20,
      marginTop: 8,
    },
    checkbox: {
      width: 20,
      height: 20,
      borderWidth: 1,
      borderColor: colors.primary,
      borderRadius: 4,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: agreeTerms ? colors.primary : 'transparent',
    },
    checkboxLabel: {
      fontSize: 14,
      color: colors.textSecondary,
      marginLeft: 10,
    },
    termsLink: {
      color: colors.primary,
      fontWeight: '500',
    },
    button: {
      padding: 16,
      borderRadius: 30,
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: 12,
      marginBottom: 4,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: isDark ? 0.3 : 0.08,
      shadowRadius: 2.5,
      elevation: 2,
    },
    primaryButton: {
      backgroundColor: colors.primary,
    },
    secondaryButton: {
      backgroundColor: colors.card,
      borderWidth: 1,
      borderColor: colors.border,
    },
    disabledButton: {
      opacity: 0.7,
    },
    buttonText: {
      color: '#fff',
      fontSize: 16,
      fontWeight: '600',
    },
    secondaryButtonText: {
      color: colors.text,
      fontSize: 16,
      fontWeight: '600',
    },
    dividerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: 24,
    },
    dividerLine: {
      flex: 1,
      height: 1,
      backgroundColor: colors.border,
    },
    dividerText: {
      color: colors.textSecondary,
      paddingHorizontal: 10,
      fontSize: 14,
    },
    loginContainer: {
      alignItems: 'center',
      marginTop: 10,
      marginBottom: 40,
    },
    loginText: {
      color: colors.textSecondary,
      fontSize: 14,
    },
    loginLink: {
      color: colors.primary,
      fontWeight: '600',
    },
    errorText: {
      color: colors.error,
      fontSize: 12,
      marginTop: 2,
      paddingHorizontal: 16,
      paddingBottom: 5,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 8,
    },
  });
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        backgroundColor={colors.surface}
        barStyle={isDark ? 'light-content' : 'dark-content'}
      />
      <View style={styles.header}>
        <Header NavName="Create an account" />
      </View>

      <ScrollView
        ref={scrollViewRef}
        style={styles.contentContainer}
        showsVerticalScrollIndicator={false}>
        <View style={styles.formContainer}>
          <View style={styles.labelContainer}>
            <Text style={styles.inputLabel}>Organization Name</Text>
          </View>
          <View style={styles.inputContainer}>
            <TextInput
              ref={organizationNameRef}
              style={styles.input}
              placeholder="Enter organization name"
              placeholderTextColor={colors.placeholder}
              value={organizationName}
              onChangeText={setOrganizationName}
            />
            {errors.organizationName && (
              <Text style={styles.errorText}>{errors.organizationName}</Text>
            )}
          </View>

          <View style={styles.labelContainer}>
            <Text style={styles.inputLabel}>Name</Text>
          </View>
          <View style={styles.inputContainer}>
            <TextInput
              ref={ownerNameRef}
              style={styles.input}
              placeholder="Enter your full name"
              placeholderTextColor={colors.placeholder}
              value={ownerName}
              onChangeText={setOwnerName}
            />
            {errors.ownerName && (
              <Text style={styles.errorText}>{errors.ownerName}</Text>
            )}
          </View>

          <View style={styles.labelContainer}>
            <Text style={styles.inputLabel}>Email</Text>
          </View>
          <View style={styles.inputContainer}>
            <TextInput
              ref={emailRef}
              style={styles.input}
              placeholder="<EMAIL>"
              placeholderTextColor={colors.placeholder}
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
            />
            {errors.email && (
              <Text style={styles.errorText}>{errors.email}</Text>
            )}
          </View>

          <View style={styles.labelContainer}>
            <Text style={styles.inputLabel}>Phone</Text>
          </View>
          <View style={styles.inputContainer}>
            <TextInput
              ref={phoneRef}
              style={styles.input}
              placeholder="Enter your phone number"
              placeholderTextColor={colors.placeholder}
              value={phone}
              onChangeText={setPhone}
              keyboardType="phone-pad"
            />
            {errors.phone && (
              <Text style={styles.errorText}>{errors.phone}</Text>
            )}
          </View>

          <View style={styles.labelContainer}>
            <Text style={styles.inputLabel}>Street Address</Text>
          </View>
          <View style={styles.inputContainer}>
            <TextInput
              ref={streetAddressRef}
              style={styles.input}
              placeholder="Enter street address"
              placeholderTextColor={colors.placeholder}
              value={streetAddress}
              onChangeText={setStreetAddress}
            />
            {errors.streetAddress && (
              <Text style={styles.errorText}>{errors.streetAddress}</Text>
            )}
          </View>

          <View style={styles.labelContainer}>
            <Text style={styles.inputLabel}>City</Text>
          </View>
          <View style={styles.inputContainer}>
            <TextInput
              ref={cityRef}
              style={styles.input}
              placeholder="Enter city"
              placeholderTextColor={colors.placeholder}
              value={city}
              onChangeText={setCity}
            />
            {errors.city && <Text style={styles.errorText}>{errors.city}</Text>}
          </View>

          <View style={styles.labelContainer}>
            <Text style={styles.inputLabel}>State</Text>
          </View>
          <View style={styles.inputContainer}>
            <TextInput
              ref={stateRef}
              style={styles.input}
              placeholder="Enter state"
              placeholderTextColor={colors.placeholder}
              value={state}
              onChangeText={setState}
            />
            {errors.state && (
              <Text style={styles.errorText}>{errors.state}</Text>
            )}
          </View>

          <View style={styles.labelContainer}>
            <Text style={styles.inputLabel}>Zip Code</Text>
          </View>
          <View style={styles.inputContainer}>
            <TextInput
              ref={zipCodeRef}
              style={styles.input}
              placeholder="Enter zip code"
              placeholderTextColor={colors.placeholder}
              value={zipCode}
              onChangeText={setZipCode}
              keyboardType="number-pad"
            />
            {errors.zipCode && (
              <Text style={styles.errorText}>{errors.zipCode}</Text>
            )}
          </View>

          <View style={styles.labelContainer}>
            <Text style={styles.inputLabel}>Password</Text>
          </View>
          <View style={styles.inputContainer}>
            <TextInput
              ref={passwordRef}
              style={styles.input}
              placeholder="••••••••••"
              placeholderTextColor={colors.placeholder}
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!passwordVisible}
            />
            <TouchableOpacity
              style={styles.eyeIcon}
              onPress={() => setPasswordVisible(!passwordVisible)}>
              <Icon
                name={passwordVisible ? 'eye-off' : 'eye'}
                size={20}
                color={colors.textSecondary}
              />
            </TouchableOpacity>
            {errors.password && (
              <Text style={styles.errorText}>{errors.password}</Text>
            )}
          </View>

          <View style={styles.labelContainer}>
            <Text style={styles.inputLabel}>Confirm Password</Text>
          </View>
          <View style={styles.inputContainer}>
            <TextInput
              ref={confirmPasswordRef}
              style={styles.input}
              placeholder="••••••••••"
              placeholderTextColor={colors.placeholder}
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry={!confirmPasswordVisible}
            />
            <TouchableOpacity
              style={styles.eyeIcon}
              onPress={() =>
                setConfirmPasswordVisible(!confirmPasswordVisible)
              }>
              <Icon
                name={confirmPasswordVisible ? 'eye-off' : 'eye'}
                size={20}
                color={colors.textSecondary}
              />
            </TouchableOpacity>
            {errors.confirmPassword && (
              <Text style={styles.errorText}>{errors.confirmPassword}</Text>
            )}
          </View>

          <View style={styles.checkboxContainer}>
            <TouchableOpacity
              style={styles.checkbox}
              onPress={() => setAgreeTerms(!agreeTerms)}>
              {agreeTerms ? <Icon name="check" size={16} color="#fff" /> : null}
            </TouchableOpacity>
            <Text style={styles.checkboxLabel}>
              By continuing you agree to our{' '}
              <Text style={styles.termsLink}>Terms of Service</Text>
            </Text>
          </View>
          {errors.agreeTerms && (
            <Text style={styles.errorText}>{errors.agreeTerms}</Text>
          )}

          <TouchableOpacity
            style={[
              styles.button,
              styles.primaryButton,
              isLoading && styles.disabledButton,
            ]}
            onPress={handleRegister}
            disabled={isLoading}>
            {isLoading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.buttonText}>
                Start your 2 days free trial
              </Text>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.button,
              styles.secondaryButton,
              isLoading && styles.disabledButton,
            ]}
            onPress={handleRegisterWithActivation}
            disabled={isLoading}>
            {isLoading ? (
              <ActivityIndicator color={colors.text} />
            ) : (
              <Text style={styles.secondaryButtonText}>
                Request for License
              </Text>
            )}
          </TouchableOpacity>

          <View style={styles.loginContainer}>
            <Text style={styles.loginText}>
              Already have an account?{' '}
              <Text
                style={styles.loginLink}
                onPress={() => navigation.navigate('LoginScreen')}>
                Sign In
              </Text>
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default RegistrationScreen;
