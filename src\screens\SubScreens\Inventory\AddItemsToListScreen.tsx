import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TextInput,
  Keyboard,
  Platform,
  ActivityIndicator,
  SafeAreaView,
} from 'react-native';
import React, {memo, useCallback, useEffect, useRef, useState} from 'react';
import Header from '../../../components/Inventory/Header';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {
  Department,
  Inventory,
  Inventory_Filter,
  Invoice_Itemized,
  Invoice_Totals,
  Vendor,
  BrandOrSubCategory,
  Brands,
  SubCategories,
} from '../../../server/types';
import {
  GetAllItemsWithFilter,
  GetItemsParamsNoFilterNoReturn,
  handleSearch,
} from '../../../utils/PublicHelper';
import {getInventoryPort} from '../../../server/InstanceTypes';
import AppSearchWIthFilter from '../../../components/Inventory/AppSearchWIthFilter';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../../styles/fonts';
import {MaterialColors} from '../../../constants/MaterialColors';
import LottieView from 'lottie-react-native';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

type AddItemsToListScreenRouteProp = RouteProp<any, 'AddItemsToListScreen'>;

interface AddItemsToListScreenProps {
  route: AddItemsToListScreenRouteProp;
  navigation: NativeStackNavigationProp<any>;
}

const AddItemsToListScreen: React.FC<AddItemsToListScreenProps> = ({
  route,
  navigation,
}) => {
  // Get params from route
  const pickListItem = route.params?.pickListItem;
  const invoiceItem = route.params?.invoiceItem || [];

  // State variables
  const [loading, setLoading] = useState<boolean>(false);
  const [inventoryData, setInventoryData] = useState<Inventory_Filter[]>([]);
  const [filteredData, setFilteredData] = useState<Inventory_Filter[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showLookup, setshowLookup] = useState<boolean>(false);
  const [filter, setFilter] = useState<boolean>(false);
  const [isEnableFilter, setIsEnableFilter] = useState<boolean>(false);
  const [cameraModal, setCameraModal] = useState<boolean>(false);

  // Pagination states
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(20);
  const [displayData, setDisplayData] = useState<Inventory_Filter[]>([]);

  // Filter states
  const [departments, setDepartments] = useState<Department[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [selectedVendor, setSelectedVendor] = useState<string>('');
  const [selectedBrand, setSelectedBrand] = useState<string>('');
  const [selectedSubCategory, setSelectedSubCategory] = useState<string>('');
  const [brandorSubCat, SetBrandSubOrCat] = useState<BrandOrSubCategory[]>([]);
  const [getBrands, setGetBrands] = useState<Brands[]>([]);
  const [getSubCategories, setGetSubCategories] = useState<SubCategories[]>([]);

  // Refs
  const textInputRef = useRef<TextInput>(null);
  const flatListRef = useRef<FlatList>(null);

  // Theme
  const colors = useThemeColors();
  const {isDark} = useTheme();

  // Check if item exists in invoice
  const isItemNumExist = (itemNum: string) => {
    return invoiceItem.some(
      (item: Invoice_Itemized) => item.ItemNum === itemNum,
    );
  };

  // Add item to pick list callback
  const addItemsToPickList = useCallback(
    (inventory: Inventory) => {
      const isExists = isItemNumExist(inventory?.ItemNum);

      if (isExists) {
        // Show alert that item already exists
        navigation.goBack();
      } else {
        navigation.navigate('AddPickListEachItems', {
          ItemData: inventory,
          PickItem: pickListItem,
        });
      }
    },
    [invoiceItem, navigation, pickListItem],
  );

  // Inventory Modal Item Component
  const InventoryModalItem = memo(
    ({
      item,
      onPress,
    }: {
      item: Inventory_Filter;
      onPress: (item: Inventory) => void;
    }) => {
      const isExists = isItemNumExist(item?.ItemNum);

      return (
        <View
          style={{
            backgroundColor: colors.card,
            marginVertical: hp('0.6%'),
            borderRadius: 15,
            paddingVertical: hp('1.5%'),
            paddingHorizontal: wp('2.5%'),
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            borderBottomWidth: 1,
            borderBottomColor: colors.border,
            opacity: isExists ? 0.5 : 1,
          }}>
          <View style={{flex: 1}}>
            <Text
              style={{
                fontFamily: Fonts.OnestBold,
                fontSize: FontSizes.small,
                color: colors.text,
              }}>
              {item.ItemName}
            </Text>
            <Text
              style={{
                fontFamily: Fonts.OnestRegular,
                fontSize: FontSizes.xSmall,
                color: colors.textSecondary,
                marginTop: hp('0.5%'),
              }}>
              {item.ItemNum}
            </Text>
            <Text
              style={{
                fontFamily: Fonts.OnestMedium,
                fontSize: FontSizes.xSmall,
                color: colors.primary,
                marginTop: hp('0.3%'),
              }}>
              ${item.Price?.toFixed(2)}
            </Text>
          </View>
        </View>
      );
    },
    (prevProps, nextProps) => {
      return prevProps.item.ItemNum === nextProps.item.ItemNum;
    },
  );

  // Empty list component
  const EmptyListComponent = memo(({searchQuery}: {searchQuery: string}) => (
    <View style={styles.emptyContainer}>
      <View style={styles.animationContainer}>
        <LottieView
          style={styles.lottie}
          source={require('../../../assets/Lotties/Nodata.json')}
          autoPlay
          loop
        />
      </View>
      <Text style={[styles.emptyText, {color: colors.text}]}>
        {searchQuery ? 'No items found for your search' : 'No items available'}
      </Text>
    </View>
  ));

  // Footer loading component
  const ListFooter = memo(({loading}: {loading: boolean}) => {
    if (!loading) return null;

    return (
      <View style={styles.footer}>
        <ActivityIndicator size="small" color={colors.primary} />
      </View>
    );
  });

  // Pagination function
  const applyPagination = (data: Inventory_Filter[], currentPage: number) => {
    const endIndex = currentPage * pageSize;
    const paginatedItems = data.slice(0, endIndex);
    setDisplayData(paginatedItems);
  };

  // Load more function
  const handleLoadMore = useCallback(() => {
    if (page * pageSize < filteredData.length && !loading) {
      const nextPage = page + 1;
      setPage(nextPage);
      applyPagination(filteredData, nextPage);
    }
  }, [page, pageSize, filteredData, loading]);

  // Apply pagination when filtered data changes
  useEffect(() => {
    if (filteredData.length > 0) {
      applyPagination(filteredData, page);
    }
  }, [filteredData, page]);

  // Get inventory data
  const getnoPage = async () => {
    setPage(1);
    GetAllItemsWithFilter(
      (await getInventoryPort()).toString(),
      '/inventorynopg',
      data => {
        setInventoryData(data);
        setFilteredData(data);
        applyPagination(data, 1);
      },
      setFilteredData,
      setLoading,
      false,
    );
  };

  // Toggle lookup function
  const toggleLookup = useCallback(
    (value: boolean) => {
      setshowLookup(value);
      applyPagination(filteredData, page);
      setSearchQuery('');
      if (Platform.OS === 'android') {
        if (value) {
          setTimeout(() => {
            if (textInputRef.current) {
              textInputRef.current.blur();
              setTimeout(() => {
                if (textInputRef.current) {
                  textInputRef.current.focus();
                }
              }, 50);
            }
          }, 50);
        } else {
          Keyboard.dismiss();
          setTimeout(() => {
            if (textInputRef.current) {
              textInputRef.current.blur();
              setTimeout(() => {
                if (textInputRef.current) {
                  textInputRef.current.focus();
                }
              }, 50);
            }
          }, 50);
        }
        return;
      }

      // iOS handling
      if (value) {
        setTimeout(() => {
          textInputRef.current?.focus();
        }, 100);
      } else {
        setSearchQuery('');
        onChangeAddItems('');
        Keyboard.dismiss();
      }
    },
    [filteredData, page],
  );

  // Handle done click
  const handleDoneClickSub = () => {
    if (textInputRef.current) {
      textInputRef.current.clear();
      textInputRef.current.blur();
    }

    setSearchQuery('');
    setshowLookup(false);
    Keyboard.dismiss();
    setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.focus();
      }
    }, 200);
    applyPagination(filteredData, page);
  };

  // Search function
  const onChangeAddItems = async (text: string) => {
    setCameraModal(false);
    const getBarcode = await GetItemsParamsNoFilterNoReturn<Inventory_Filter>(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      {ItemNum: text},
    );
    setSearchQuery(getBarcode[0]?.ItemNum || text);

    if (showLookup) {
      handleSearch(
        text,
        filteredData,
        ['ItemName', 'ItemNum'],
        setDisplayData,
        setLoading,
      );
    } else {
      if (text.length > 0) {
        if (getBarcode && getBarcode.length > 0) {
          const isExists = isItemNumExist(getBarcode[0]?.ItemNum);

          if (isExists) {
            // Item already exists, show alert and reset
            if (textInputRef.current) {
              textInputRef.current.clear();
              textInputRef.current.blur();
            }

            setSearchQuery('');
            setshowLookup(false);
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 200);
          } else {
            navigation.navigate('AddPickListEachItems', {
              ItemData: getBarcode[0],
              PickItem: pickListItem,
            });
          }
        }
      } else {
        setDisplayData(filteredData);
      }
    }
  };

  // Load data on focus
  useFocusEffect(
    useCallback(() => {
      getnoPage();
    }, []),
  );

  return (
    <SafeAreaView
      style={[styles.container, {backgroundColor: colors.background}]}>
      <View style={{width: '95%'}}>
        <Header
          NavName="Add Items"
          Onpress={() => navigation.goBack()}
          isProvid={true}
          isOption={true}
          Options={() => {
            if (textInputRef.current) {
              textInputRef.current.clear();
              textInputRef.current.blur();
            }

            setSearchQuery('');
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 200);

            setCameraModal(!cameraModal);
          }}
        />
      </View>

      <View style={{paddingBottom: hp('1%')}}>
        <AppSearchWIthFilter
          OnSearch={onChangeAddItems}
          SearchValue={searchQuery}
          OnSearchSet={() => setFilter(true)}
          isEnableFilter={isEnableFilter}
          Keyboardon={showLookup}
          textInputRef={textInputRef}
          onToggleLookup={value => toggleLookup(value)}
          OnSubmitEditing={() => handleDoneClickSub()}
        />

        <Text
          style={{
            fontSize: FontSizes.medium,
            fontFamily: Fonts.OnestBold,
            color: colors.text,
            paddingVertical: hp('1%'),
          }}>{`Total Items: (${filteredData.length || 0})`}</Text>
      </View>

      <View style={{flex: 1}}>
        <FlatList
          ref={flatListRef}
          data={displayData}
          renderItem={({item}) => (
            <InventoryModalItem item={item} onPress={addItemsToPickList} />
          )}
          keyExtractor={(item: Inventory_Filter) => item.ItemNum.toString()}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          getItemLayout={(_, index) => ({
            length: 88,
            offset: 88 * index,
            index,
          })}
          ListEmptyComponent={<EmptyListComponent searchQuery={searchQuery} />}
          ListFooterComponent={<ListFooter loading={loading} />}
          initialNumToRender={10}
          maxToRenderPerBatch={5}
          windowSize={10}
          removeClippedSubviews={true}
          updateCellsBatchingPeriod={75}
          showsVerticalScrollIndicator={false}
          maintainVisibleContentPosition={{
            minIndexForVisible: 0,
          }}
          contentContainerStyle={{
            paddingBottom: hp('2%'),
          }}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: wp('2.5%'),
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: hp('10%'),
  },
  animationContainer: {
    width: wp('40%'),
    height: hp('20%'),
  },
  lottie: {
    width: '100%',
    height: '100%',
  },
  emptyText: {
    fontSize: FontSizes.medium,
    fontFamily: Fonts.OnestMedium,
    textAlign: 'center',
    marginTop: hp('2%'),
  },
  footer: {
    paddingVertical: hp('2%'),
    alignItems: 'center',
  },
});

export default AddItemsToListScreen;
