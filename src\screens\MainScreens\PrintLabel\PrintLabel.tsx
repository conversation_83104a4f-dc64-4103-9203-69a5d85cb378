import {
  View,
  Text,
  StyleSheet,
  TextInput,
  FlatList,
  ActivityIndicator,
  Alert,
  TouchableOpacity,
  Modal,
  Keyboard,
  ScrollView,
  Platform,
  TouchableWithoutFeedback,
} from 'react-native';
import React, {useCallback, useEffect, useRef, useState, memo} from 'react';
import {
  Backround,
  Primary,
  Secondary,
  SecondaryHint,
} from '../../../constants/Color';
import Header from '../../../components/Inventory/Header';
import AppDropDown from '../../../components/Inventory/AppDropDown';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import AppButton from '../../../components/Inventory/AppButton';
import {
  RouteProp,
  useFocusEffect,
  useIsFocused,
} from '@react-navigation/native';
import {
  BrandOrSubCategory,
  Brands,
  Department,
  Inventory,
  Inventory_Filter,
  Printers,
  SubCategories,
  Vendor,
} from '../../../server/types';
import Search from '../../../components/Inventory/Search';
import {
  GetAllItems,
  GetAllItemsWithFilter,
  GetItemsParamsNoFilterNoReturn,
  handleSearch,
  onSearchChange_Common,
  showAlert,
  showAlertOK,
  updateData,
} from '../../../utils/PublicHelper';
import {updateItem} from '../../../server/service';
import DataList from '../../../components/Inventory/AppList';
import {getInventoryPort, getLotteryPort} from '../../../server/InstanceTypes';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../../styles/fonts';
import AppFilter from '../../../components/Inventory/AppFilter';
import AppSearchWIthFilter from '../../../components/Inventory/AppSearchWIthFilter';
import CustomCheckbox from '../../../components/Inventory/CustomCheckbox';
import {applyDefaults} from '../../../Validator/Inventory/Barcode';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  sendPrintLabelBulkPricing,
  sendPrintLabelFive,
  sendPrintLabelFour,
  sendPrintLabelMixAndMatch,
  sendPrintLabelOne,
  sendPrintLabelRound,
  sendPrintLabelSalePricing,
  sendPrintLabelSix,
  sendPrintLabelThree,
  sendPrintLabelTwo,
} from '../../../utils/PrinterHelper';
import {MaterialColors} from '../../../constants/MaterialColors';
import FAB from '../../../components/common/FAB';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Icon from 'react-native-vector-icons/FontAwesome';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import LottieView from 'lottie-react-native';
import AppLoader from '../../../components/Inventory/AppLoader';
import {ServerConnection} from '../../../Validator/Inventory/ServerValidation';
import AppScanner from '../../../components/Inventory/AppScanner';
import {useCodeScanner} from 'react-native-vision-camera';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

type CountItemRouteProp = RouteProp<any, 'AdjustStock'>;

// Memoized Item component for optimal rendering performance
const PrintLabelItem = memo(
  ({
    item,
    onPress,
    inputValue,
    action,
    onClearItem,
    colors,
    isDark,
  }: {
    item: Inventory_Filter;
    onPress: () => void;
    inputValue: string;
    action: boolean;
    onClearItem: () => void;
    colors: any;
    isDark: boolean;
  }) => {
    const hasInputValue = inputValue !== '';

    return (
      <TouchableOpacity onPress={onPress}>
        <View
          style={[
            {
              flexDirection: 'row',
              alignItems: 'center',
              paddingVertical: hp('0.8%'),
              paddingHorizontal: wp('2%'),
              borderBottomWidth: 0.5,
              borderBottomColor: colors.border,
              backgroundColor: isDark ? colors.surface : colors.background,
              borderRadius: 6,
              marginBottom: 4,
            },
            hasInputValue && {
              borderLeftWidth: 2,
              borderLeftColor: colors.primary,
            },
          ]}>
          {hasInputValue && (
            <TouchableOpacity
              style={{
                backgroundColor: colors.error,
                borderRadius: 20,
                width: 16,
                height: 16,
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 4,
              }}
              onPress={onClearItem}>
              <Icon name="minus" size={8} color="#FFFFFF" />
            </TouchableOpacity>
          )}

          {/* Added icon container with printer-related icon */}
          <View
            style={{
              marginRight: wp('1.5%'),
              justifyContent: 'center',
              alignItems: 'center',
              width: wp('7%'),
              height: wp('7%'),
              borderRadius: wp('3.5%'),
              backgroundColor: colors.primary + '15',
            }}>
            <MaterialIcons
              name="print"
              size={wp('3.8%')}
              color={colors.primary}
            />
          </View>

          <View
            style={{
              flex: 1,
              gap: 1,
            }}>
            <Text
              style={{
                fontSize: wp('3%'),
                fontFamily: Fonts.OnestBold,
                color: colors.text,
                marginBottom: 1,
              }}
              numberOfLines={1}
              ellipsizeMode="tail">
              {item.ItemName}
            </Text>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
              }}>
              {/* Added icon to price */}
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginRight: wp('2%'),
                }}>
                <MaterialIcons
                  name="attach-money"
                  size={wp('2.2%')}
                  color={colors.textSecondary}
                />
                <Text
                  style={{
                    fontSize: FontSizes.small,
                    fontFamily: Fonts.OnestMedium,
                    color: colors.textSecondary,
                    marginRight: wp('2%'),
                  }}>
                  {item.Price}
                </Text>
              </View>

              {!action ? (
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    gap: wp('0.5%'),
                  }}>
                  <MaterialIcons
                    name="storage"
                    size={wp('2.2%')}
                    color={colors.primary}
                  />
                  <Text
                    style={{
                      fontSize: FontSizes.small,
                      fontFamily: Fonts.OnestMedium,
                      color: colors.primary,
                    }}>
                    In: {item.In_Stock}
                  </Text>
                </View>
              ) : (
                <>
                  {inputValue !== '' ? (
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          gap: wp('0.5%'),
                        }}>
                        <MaterialIcons
                          name="storage"
                          size={wp('2.2%')}
                          color={colors.primary}
                        />
                        <Text
                          style={{
                            fontSize: FontSizes.small,
                            fontFamily: Fonts.OnestMedium,
                            color: colors.primary,
                          }}>
                          In Stock: {item.In_Stock}
                        </Text>
                      </View>
                      <Text
                        style={{
                          fontSize: FontSizes.small,
                          color: colors.textSecondary,
                          marginHorizontal: wp('1%'),
                        }}>
                        •
                      </Text>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          gap: wp('0.5%'),
                        }}>
                        <MaterialIcons
                          name="qr-code"
                          size={wp('2.2%')}
                          color={colors.primary}
                        />
                        <Text
                          style={{
                            fontSize: FontSizes.small,
                            fontFamily: Fonts.OnestMedium,
                            color: colors.primary,
                          }}>
                          No Of Labels: {inputValue}
                        </Text>
                      </View>
                    </View>
                  ) : (
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: wp('0.5%'),
                      }}>
                      <MaterialIcons
                        name="storage"
                        size={wp('2.2%')}
                        color={colors.primary}
                      />
                      <Text
                        style={{
                          fontSize: FontSizes.small,
                          fontFamily: Fonts.OnestMedium,
                          color: colors.primary,
                        }}>
                        In Stock: {item.In_Stock}
                      </Text>
                    </View>
                  )}
                </>
              )}
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  },
  (prevProps, nextProps) => {
    // Only re-render if item, input value, or theme changes
    return (
      prevProps.item.ItemNum === nextProps.item.ItemNum &&
      prevProps.inputValue === nextProps.inputValue &&
      prevProps.action === nextProps.action &&
      prevProps.isDark === nextProps.isDark &&
      JSON.stringify(prevProps.colors) === JSON.stringify(nextProps.colors)
    );
  },
);

const PrintLabel: React.FC<{route: CountItemRouteProp; navigation: any}> = ({
  route,
  navigation,
}) => {
  // Main data state
  const [masterData, setMasterData] = useState<Inventory_Filter[]>([]);
  const [filteredData, setFilteredData] = useState<Inventory_Filter[]>([]);
  const [displayData, setDisplayData] = useState<Inventory_Filter[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [initialLoading, setInitialLoading] = useState<boolean>(true);

  // Pagination state
  const [page, setPage] = useState<number>(1);
  const ITEMS_PER_PAGE = 20;

  // UI state
  const [action, setAction] = useState<boolean>(true);
  const [filter, setFilter] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState<string>('');
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [showLookup, setshowLookup] = useState<boolean>(false);
  const [focus, setFocus] = useState<boolean>(true);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [printerConfig, setPrinterConfig] = useState<boolean>(false);
  const [modalItemId, setModalItemId] = useState<string | null>(null);
  const [modalStockInput, setModalStockInput] = useState<string>('');

  // Input tracking
  const [inputValues, setInputValues] = useState<
    {itemNum: string; value: string}[]
  >([]);
  const [newStockValues, setNewStockValues] = useState<{[key: string]: string}>(
    {},
  );
  const [editingItem, setEditingItem] = useState(null);

  // Filter state
  const [departments, setDepartments] = useState<Department[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [selectedVendor, setSelectedVendor] = useState<string>('');
  const [selectedBrand, setSelectedBrand] = useState<string>('');
  const [selectedSubCategory, setSelectedSubCategory] = useState<string>('');
  const [brandorSubCat, SetBrandSubOrCat] = useState<BrandOrSubCategory[]>([]);
  const [getBrands, setGetBrands] = useState<Brands[]>([]);
  const [getSubCategories, setGetSubCategories] = useState<SubCategories[]>([]);
  const [isEnableFilter, setIsEnableFilter] = useState<boolean>(false);

  // Printer state
  const [printers, setPrinters] = useState<Printers[]>([]);
  const [selectedPrinters, setSelectedPrinters] = useState<string>('');
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [getCompany, setGetCompany] = useState<any[]>([]);

  const textInputRef = useRef<TextInput>(null);
  const flatListRef = useRef<FlatList>(null);
  const [appLoader, setAppLoader] = useState<boolean>(true);
  const [camera, setCamera] = useState(false);
  const [isAlertVisible, setIsAlertVisible] = useState(false);

  // Theme hooks
  const colors = useThemeColors();
  const {isDark} = useTheme();

  // Initialize data on screen focus
  useFocusEffect(
    useCallback(() => {
      checkServerConnection();
    }, []),
  );

  const checkServerConnection = async () => {
    const inventoryPort = await getInventoryPort();
    const serverStatus = await ServerConnection(inventoryPort);
    if (!serverStatus) {
      showAlertOK(
        'Database Connection Falied, Please Check Your Database Configuration',
        'Connection Failed',
        'OK',
      );
    } else {
      setInitialLoading(true);
      invNoPagination();
      getPrinters();
      getCompanyDetials();
      setFilter(false);
      setSearchQuery('');
      setPage(1);
      setshowLookup(false);
      // Focus the text input for scanning
      setTimeout(() => {
        if (textInputRef.current) {
          textInputRef.current.blur();
          setTimeout(() => {
            if (textInputRef.current) {
              textInputRef.current.focus();
            }
          }, 50);
        }
      }, 50);
    }
  };
  // Apply search debouncing
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 300);

    return () => {
      clearTimeout(handler);
    };
  }, [searchQuery]);

  // Check if filters are enabled
  useEffect(() => {
    if (
      selectedBrand ||
      selectedDepartment ||
      selectedVendor ||
      selectedSubCategory
    ) {
      setIsEnableFilter(true);
    } else {
      setIsEnableFilter(false);
    }
  }, [selectedBrand, selectedDepartment, selectedVendor, selectedSubCategory]);

  // Apply filters and search
  useEffect(() => {
    // Skip if masterData isn't loaded yet
    if (masterData.length === 0) return;

    // Reset pagination when filters change
    setPage(1);

    // Filter by department, vendor, brand, and subcategory
    const filtered = masterData.filter(item => {
      const matchesDepartment = selectedDepartment
        ? item.Dept_ID === selectedDepartment
        : true;
      const matchesVendor = selectedVendor
        ? item.Vendor_Number === selectedVendor
        : true;
      const matchesBrand = selectedBrand ? item.Brand === selectedBrand : true;
      const matchesSubCategory = selectedSubCategory
        ? item.SubCategory === selectedSubCategory
        : true;

      // Also apply search filter if we have a query
      const searchTerm = debouncedQuery.toLowerCase();
      const matchesSearch = !debouncedQuery
        ? true
        : (item.ItemNum?.toString().toLowerCase() || '').includes(searchTerm) ||
          (item.ItemName?.toLowerCase() || '').includes(searchTerm);

      return (
        matchesDepartment &&
        matchesVendor &&
        matchesBrand &&
        matchesSubCategory &&
        matchesSearch
      );
    });

    // Sort data - put items with input values first
    const sortedFiltered = [...filtered].sort((a, b) => {
      const aHasInput = inputValues.some(
        input => input.itemNum === a.ItemNum && input.value,
      );
      const bHasInput = inputValues.some(
        input => input.itemNum === b.ItemNum && input.value,
      );

      if (aHasInput && !bHasInput) return -1; // a comes first
      if (!aHasInput && bHasInput) return 1; // b comes first
      return 0; // keep original order if both have or don't have inputs
    });

    setFilteredData(sortedFiltered);
    applyPagination(sortedFiltered, 1);
  }, [
    selectedDepartment,
    selectedVendor,
    selectedBrand,
    selectedSubCategory,
    debouncedQuery,
    masterData,
    inputValues, // Add inputValues to ensure re-sort when values change
  ]);

  // Pagination function
  const applyPagination = useCallback(
    (data: Inventory_Filter[], currentPage: number) => {
      const endIndex = currentPage * ITEMS_PER_PAGE;
      const paginatedItems = data.slice(0, endIndex);
      setDisplayData(paginatedItems);
    },
    [ITEMS_PER_PAGE],
  );

  // Load more data when reaching the end of the list
  const handleLoadMore = useCallback(() => {
    if (loading) return;

    if (page * ITEMS_PER_PAGE < filteredData.length) {
      const nextPage = page + 1;
      setPage(nextPage);
      applyPagination(filteredData, nextPage);
    }
  }, [page, filteredData, ITEMS_PER_PAGE, loading, applyPagination]);

  // Apply pagination when page changes
  useEffect(() => {
    if (filteredData.length > 0 && page > 1) {
      applyPagination(filteredData, page);
    }
  }, [page, filteredData, applyPagination]);

  // Fetch inventory data with pagination
  const invNoPagination = async () => {
    try {
      setLoading(true);
      const port = await getInventoryPort();
      const data = await GetAllItemsWithFilter(
        port.toString(),
        '/getInventoryFilter',
        data => {
          setMasterData(data);
          setFilteredData(data);
          applyPagination(data, 1);
          setInitialLoading(false);
          setLoading(false);
        },
        // This is a callback for displayData - we'll handle it ourselves
        displayData => {},
        setLoading,
        false,
      );
    } catch (error) {
      console.error('Error fetching inventory:', error);
      setInitialLoading(false);
      setLoading(false);
    }
  };

  const getCompanyDetials = async () => {
    GetAllItems<any[]>(
      (await getInventoryPort()).toString(),
      '/getCompanyDetails',
      setGetCompany,
      setLoading,
    );
  };

  const getPrinters = async () => {
    GetAllItems<Printers[]>(
      (await getInventoryPort()).toString(),
      '/GetAllprinters',
      setPrinters,
      setLoading,
    );

    const getAssignedPrinter = await AsyncStorage.getItem('PRINTER');
    if (getAssignedPrinter) {
      setSelectedPrinters(getAssignedPrinter);
    }
  };

  // Pull to refresh implementation
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    setPage(1);

    try {
      await invNoPagination();
    } catch (error) {
      console.error('Error refreshing data:', error);
    }

    setRefreshing(false);
  }, []);

  const openModalForItem = useCallback(
    (itemNum: string) => {
      setModalItemId(itemNum);
      setModalStockInput(newStockValues[itemNum] || '');
      setModalVisible(true);
    },
    [newStockValues],
  );

  const handleInputChange = useCallback((value: string, itemNum: string) => {
    setInputValues(prevValues => {
      const existingItemIndex = prevValues.findIndex(
        item => item.itemNum === itemNum,
      );

      if (existingItemIndex !== -1) {
        const updatedValues = [...prevValues];
        updatedValues[existingItemIndex].value = value;
        return updatedValues;
      } else {
        return [...prevValues, {itemNum, value}];
      }
    });

    setEditingItem(itemNum);
    setModalStockInput(value);
  }, []);

  // Clear selected items
  const clearSelectedItems = useCallback(() => {
    setInputValues([]);
    setNewStockValues({});
    setModalStockInput('');
    setModalItemId(null);
    setModalVisible(false);

    // If you're keeping track of which item is being edited
    if (setEditingItem) {
      setEditingItem(null);
    }
  }, []);

  const handleDoneClick = useCallback(() => {
    setSearchQuery('');
    setEditingItem(null);
    if (modalItemId) {
      setNewStockValues(prev => ({
        ...prev,
        [modalItemId]: modalStockInput,
      }));
    }
    setModalVisible(false);
    setModalItemId(null);
    setModalStockInput('');
    if (textInputRef.current) {
      textInputRef.current.clear();
      textInputRef.current.blur();
    }

    setSearchQuery('');
    setshowLookup(false);
    Keyboard.dismiss();
    setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.focus();
      }
    }, 200);
  }, [modalItemId, modalStockInput]);

  // Sort data to prioritize items with input values

  const clearEachItem = useCallback(
    (item: Inventory_Filter) => {
      // Remove the specific item from inputValues
      setInputValues(prevValues =>
        prevValues.filter(input => input.itemNum !== item.ItemNum),
      );

      setNewStockValues(prev => {
        const {[item.ItemNum]: _, ...rest} = prev; // Remove the item's key
        return rest;
      });

      // If the modal is currently showing this item, clear its input too
      if (modalItemId === item.ItemNum) {
        setModalStockInput('');
      }
    },
    [modalItemId],
  );

  const renderItem = useCallback(
    ({item}) => {
      const inputValue =
        inputValues.find(input => input.itemNum === item.ItemNum)?.value || '';

      return (
        <PrintLabelItem
          item={item}
          onPress={() => openModalForItem(item.ItemNum)}
          inputValue={inputValue}
          action={action}
          onClearItem={() => clearEachItem(item)}
          colors={colors}
          isDark={isDark}
        />
      );
    },
    [inputValues, action, openModalForItem, clearEachItem, colors, isDark],
  );

  const handleOutsidePress = useCallback(() => {
    if (showLookup) {
      setshowLookup(false);
      Keyboard.dismiss();
    }
  }, [showLookup]);

  const onSearchChange = useCallback(
    async (text: string) => {
      setCamera(false);
      if (text === '') {
        setSearchQuery('');
        setFilteredData(masterData);
        applyPagination(filteredData, 1);
        return;
      } else {
        const getBarcode =
          await GetItemsParamsNoFilterNoReturn<Inventory_Filter>(
            (await getInventoryPort()).toString(),
            '/inventory/:ItemNum',
            {ItemNum: text},
          );
        setSearchQuery(getBarcode[0]?.ItemNum || text);
        if (showLookup) {
          handleSearch(
            text,
            masterData,
            ['ItemName', 'ItemNum'],
            setFilteredData,
            setLoading,
          );
        } else {
          if (Array.isArray(getBarcode) && getBarcode.length === 0) {
            setCamera(false);
            const userConfirmed = await showAlert(
              'Item not found. Do you want to create a new item?',
            );
            if (userConfirmed) {
              navigation.navigate('ItemType', {
                ItemData: getBarcode[0]?.ItemNum || text,
              });
            } else {
              if (textInputRef.current) {
                textInputRef.current.clear();
                textInputRef.current.blur();
              }

              setSearchQuery('');
              Keyboard.dismiss();
              setTimeout(() => {
                if (textInputRef.current) {
                  textInputRef.current.focus();
                }
              }, 200);
            }
          } else {
            setCamera(false);
            handleSearch(
              getBarcode[0]?.ItemNum,
              masterData,
              ['ItemName', 'ItemNum'],
              setFilteredData,
              setLoading,
            );
            openModalForItem(getBarcode[0]?.ItemNum);
          }
        }
      }
    },
    [showLookup, masterData, openModalForItem, navigation],
  );

  const toggleLookup = useCallback((value: boolean) => {
    applyPagination(filteredData, page);
    setshowLookup(value);
    setSearchQuery('');
    if (Platform.OS === 'android') {
      if (value) {
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.blur();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 50);
          }
        }, 50);
      } else {
        Keyboard.dismiss();
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.blur();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 50);
          }
        }, 50);
      }
      return;
    }

    // iOS handling
    if (value) {
      setTimeout(() => {
        textInputRef.current?.focus();
      }, 100);
    } else {
      Keyboard.dismiss();
    }
  }, []);

  const selectPrinter = useCallback(async (printer: Printers) => {
    setSelectedPrinters(printer.name);
    await AsyncStorage.setItem('PRINTER', printer.name);
    setPrinterConfig(false);
  }, []);

  const templateOptions = [
    {label: 'Shelf Label 2.25"x 1.25"', value: 'Label1'},
    {label: 'Shelf Label 2"x 1"', value: 'Label2'},
    {label: 'Shelf Label 2.36"x 1.18"', value: 'Label3'},
    {label: 'Shelf Label 2.25"x 1"', value: 'Label4'},
    {label: 'Product Label 1"x 1"', value: 'Label5'},
    {label: 'Product Label 1.5"x 0.5"', value: 'Label6'},
    {label: 'Promotional Bulk Pricing Label 2.362"x 3.937"', value: 'Label7'},
    {label: 'Promotional Sale Pricing Label 2.362"x 3.937"', value: 'Label8'},
    {label: 'Promotional Mix N Match Label 2.362"x 3.937"', value: 'Label9'},
    {label: 'Deli Circle Label 2.75"', value: 'Label10'},
  ];

  const onPrintLabels = useCallback(async () => {
    if (
      selectedPrinters === '' ||
      selectedPrinters === null ||
      selectedTemplate === ''
    ) {
      Alert.alert('Please select a printer and a label template.');
      return;
    }

    if (inputValues.length > 0) {
      inputValues.map(async print => {
        const labelDetails = await GetItemsParamsNoFilterNoReturn(
          (await getInventoryPort()).toString(),
          '/inventory/:ItemNum',
          {ItemNum: print.itemNum},
        );

        for (let i = 0; i < Number(print.value); i++) {
          handleCondition(selectedTemplate, labelDetails[0]);
        }
      });
    } else {
      Alert.alert('Please Select the Item to Print Label');
    }
  }, [selectedPrinters, selectedTemplate, inputValues]);

  const handleCondition = useCallback(
    (condition: string, SendLabelDetails: Inventory) => {
      switch (condition) {
        case 'Label1':
          sendPrintLabelOne(SendLabelDetails, selectedPrinters, getCompany);
          break;

        case 'Label2':
          sendPrintLabelTwo(SendLabelDetails, selectedPrinters, getCompany);
          break;

        case 'Label3':
          sendPrintLabelThree(SendLabelDetails, selectedPrinters, getCompany);
          break;

        case 'Label4':
          sendPrintLabelFour(SendLabelDetails, selectedPrinters, getCompany);
          break;

        case 'Label5':
          sendPrintLabelFive(SendLabelDetails, selectedPrinters, getCompany);
          break;

        case 'Label6':
          sendPrintLabelSix(SendLabelDetails, selectedPrinters, getCompany);
          break;

        case 'Label7':
          sendPrintLabelBulkPricing(
            SendLabelDetails,
            selectedPrinters,
            getCompany,
          );
          break;

        case 'Label8':
          sendPrintLabelSalePricing(
            SendLabelDetails,
            selectedPrinters,
            getCompany,
          );
          break;

        case 'Label9':
          sendPrintLabelMixAndMatch(
            SendLabelDetails,
            selectedPrinters,
            getCompany,
          );
          break;

        case 'Label10':
          sendPrintLabelRound(SendLabelDetails, selectedPrinters, getCompany);
          break;

        default:
          console.log('Unknown condition.');
      }
    },
    [selectedPrinters, getCompany],
  );

  // Optimized key extractor
  const keyExtractor = useCallback(
    (item: Inventory_Filter) => item.ItemNum.toString(),
    [],
  );

  // GetItemLayout for optimized rendering
  const getItemLayout = useCallback(
    (_, index) => ({
      length: 88, // approximate height of item
      offset: 88 * index,
      index,
    }),
    [],
  );

  const handleDoneClickSub = () => {
    if (textInputRef.current) {
      textInputRef.current.clear();
      textInputRef.current.blur();
    }

    setSearchQuery('');
    setshowLookup(false);
    Keyboard.dismiss();
    setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.focus();
      }
    }, 200);
    applyPagination(filteredData, page);
  };

  const codeScanner = useCodeScanner({
    codeTypes: [
      'qr',
      'ean-13',
      'upc-a',
      'ean-8',
      'upc-e',
      'code-128',
      'code-39',
      'code-93',
    ],
    onCodeScanned: codes => {
      if (codes.length > 0 && !isAlertVisible) {
        onSearchChange(codes[0].value);
      }
    },
  });

  // Empty list component
  const EmptyListComponent = memo(({searchQuery}: {searchQuery: string}) => (
    <View style={styles.emptyContainer}>
      <View style={styles.animationContainer}>
        <LottieView
          style={styles.lottie}
          source={require('../../../assets/Lotties/Nodata.json')}
          autoPlay
          loop
        />
      </View>
      <Text style={styles.emptyText}>
        {searchQuery
          ? 'No matching items found'
          : 'No inventory items available'}
      </Text>
    </View>
  ));

  // Footer loading component
  const ListFooter = memo(({loading}: {loading: boolean}) => {
    if (!loading) return null;
    const [appLoader, setAppLoader] = useState<boolean>(true);

    return (
      <View style={styles.footer}>
        <AppLoader
          modalVisible={appLoader}
          setModalVisible={setAppLoader}
          isLookup={true}
        />
        <Text style={styles.footerText}>Loading more items...</Text>
      </View>
    );
  });

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: 16,
      justifyContent: 'space-between',
    },
    iconContainer: {
      marginRight: wp('1.5%'),
      justifyContent: 'center',
      alignItems: 'center',
      width: wp('7%'),
      height: wp('7%'),
      borderRadius: wp('3.5%'),
      backgroundColor: colors.primary + '15',
    },
    priceContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: wp('2%'),
    },
    stockIconContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: wp('0.5%'),
    },
    labelIconContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: wp('0.5%'),
    },
    row: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: hp('0.8%'),
      paddingHorizontal: wp('2%'),
      borderBottomWidth: 0.5,
      borderBottomColor: colors.border,
      backgroundColor: colors.surface,
      borderRadius: 6,
      marginBottom: 4,
    },
    minusButton: {
      backgroundColor: colors.error,
      borderRadius: 20,
      width: 16,
      height: 16,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 4,
    },
    itemContentContainer: {
      flex: 1,
      gap: 1,
    },
    itemNameText: {
      fontSize: wp('3%'),
      fontFamily: Fonts.OnestBold,
      color: colors.text,
      marginBottom: 1,
    },
    itemDetailsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    priceText: {
      fontSize: FontSizes.small,
      fontFamily: Fonts.OnestMedium,
      color: colors.textSecondary,
      marginRight: wp('2%'),
    },
    stockText: {
      fontSize: FontSizes.small,
      fontFamily: Fonts.OnestMedium,
      color: colors.primary,
    },
    stockLabelContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    dotSeparator: {
      fontSize: FontSizes.small,
      color: colors.textSecondary,
      marginHorizontal: wp('1%'),
    },
    headerContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    headerTitle: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.xLarge,
      paddingVertical: hp('2%'),
    },
    printerConfigButton: {
      padding: 10,
      borderRadius: 8,
      justifyContent: 'center',
      alignItems: 'center',
      elevation: 2,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: isDark ? 0.3 : 0.2,
      shadowRadius: 1.5,
    },
    labelDropdown: {
      width: '65%',
      alignSelf: 'flex-start',
    },
    itemsHeaderContainer: {
      marginTop: 10,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    availableItemsText: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestBold,
    },
    itemCountText: {
      fontFamily: Fonts.OnestMedium,
      fontSize: FontSizes.medium,
    },
    listContainer: {
      marginTop: 10,
      height: hp('57%'),
    },
    listContent: {
      paddingBottom: hp('10%'),
    },
    bottomButtonContainer: {
      position: 'absolute',
      left: 0,
      right: 0,
      bottom: 4,
      paddingHorizontal: wp('2.5%'),
    },
    buttonWrapper: {
      height: '100%',
      width: '100%',
    },
    buttonWrapperRow: {
      height: '100%',
      width: '100%',
      flexDirection: 'row',
      paddingHorizontal: 5,
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    buttonContainer: {
      width: '100%',
      paddingBottom: hp('1%'),
    },
    modalContainer: {
      flex: 1,
      backgroundColor: 'rgba(0,0,0,0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
      width: '80%',
      padding: 20,
      borderRadius: 10,
      alignItems: 'center',
    },
    modalHeader: {
      flexDirection: 'row',
      width: '100%',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 10,
    },
    modalTitle: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      marginBottom: 10,
    },
    modalCloseButton: {
      padding: 5,
    },
    modalCloseButtonText: {
      fontSize: FontSizes.medium,
    },
    modalInput: {
      borderWidth: 1,
      borderRadius: 5,
      width: '100%',
      height: 40,
      paddingHorizontal: 10,
      marginBottom: 20,
    },
    modalButtonContainer: {
      width: '45%',
    },
    selectedPrinterText: {
      marginTop: -15,
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestMedium,
    },
    printersListContainer: {
      width: '100%',
      height: '80%',
      marginTop: 10,
    },
    printerItem: {
      paddingVertical: 8,
      paddingHorizontal: 10,
      marginBottom: 10,
      borderRadius: 4,
    },
    printerItemText: {
      fontFamily: Fonts.OnestMedium,
    },
    filterContainer: {
      flex: 1,
    },
    clearAllButton: {
      paddingHorizontal: 10,
      paddingVertical: 5,
      borderRadius: 4,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    clearAllText: {
      color: '#FFFFFF',
      fontFamily: Fonts.OnestMedium,
      fontSize: FontSizes.medium,
    },
    clearAllIcon: {
      marginRight: 5,
    },
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: hp('10%'),
    },
    lottie: {
      height: 200,
      width: 200,
      backgroundColor: 'transparent',
    },
    emptyText: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: 20,
      fontFamily: Fonts.OnestMedium,
    },
    footer: {
      padding: 15,
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    footerText: {
      marginLeft: 10,
      fontFamily: Fonts.OnestMedium,
      color: colors.textSecondary,
    },
    centered: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: hp('10%'),
    },
  });

  return (
    <TouchableWithoutFeedback onPress={handleOutsidePress}>
      <View style={[styles.container, {backgroundColor: colors.background}]}>
        <View>
          <View style={styles.headerContainer}>
            <Text style={[styles.headerTitle, {color: colors.text}]}>
              Print Label
            </Text>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 8}}>
              <TouchableOpacity
                style={[
                  styles.printerConfigButton,
                  {
                    backgroundColor: colors.primary,
                    shadowColor: colors.shadow,
                  },
                ]}
                onPress={() => setPrinterConfig(true)}>
                <Icon name="print" size={15} color="#FFFFFF" />
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.printerConfigButton,
                  {
                    backgroundColor: colors.primary,
                    shadowColor: colors.shadow,
                  },
                ]}
                onPress={() => navigation.navigate('LabelMarginConfig')}>
                {/* <MaterialCommunityIcons
                  name="format-line-spacing"
                  size={15}
                  color="#FFFFFF"
                /> */}
                <Icon name="indent" size={15} color="#FFFFFF" />
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.printerConfigButton,
                  {
                    backgroundColor: colors.primary,
                    shadowColor: colors.shadow,
                  },
                ]}
                onPress={() => {
                  if (textInputRef.current) {
                    textInputRef.current.clear();
                    textInputRef.current.blur();
                  }
                  setSearchQuery('');
                  Keyboard.dismiss();
                  setTimeout(() => {
                    if (textInputRef.current) {
                      textInputRef.current.focus();
                    }
                  }, 200);
                  setCamera(!camera);
                }}>
                <MaterialCommunityIcons
                  name="camera"
                  size={15}
                  color="#FFFFFF"
                />
              </TouchableOpacity>
            </View>
          </View>

          <AppSearchWIthFilter
            OnSearch={(text: string) => onSearchChange(text)}
            SearchValue={searchQuery}
            OnSearchSet={() => setFilter(true)}
            isEnableFilter={isEnableFilter}
            Keyboardon={showLookup}
            textInputRef={textInputRef}
            onToggleLookup={value => toggleLookup(value)}
            OnSubmitEditing={handleDoneClickSub}
          />
          <View style={styles.labelDropdown}>
            <AppDropDown
              OptionLabel="Select the Template"
              label=""
              options={templateOptions}
              selectedValue={selectedTemplate}
              onSelect={value => setSelectedTemplate(value)}
              isRequired={true}
            />
          </View>

          <View style={styles.itemsHeaderContainer}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <Text style={[styles.availableItemsText, {color: colors.text}]}>
                Available Items
              </Text>
              <Text
                style={[
                  styles.itemCountText,
                  {color: colors.textSecondary},
                ]}>{` (${filteredData.length})`}</Text>
            </View>
            <View>
              {inputValues.length > 0 && (
                <TouchableOpacity
                  style={[
                    styles.clearAllButton,
                    {backgroundColor: colors.error},
                  ]}
                  onPress={clearSelectedItems}>
                  <Icon
                    name="trash"
                    size={15}
                    color="#FFFFFF"
                    style={styles.clearAllIcon}
                  />
                  <Text style={styles.clearAllText}>Clear All</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>

          <View style={styles.listContainer}>
            {initialLoading ? (
              <View style={styles.centered}>
                <AppLoader
                  modalVisible={appLoader}
                  setModalVisible={setAppLoader}
                  isLookup={true}
                />
              </View>
            ) : (
              <FlatList
                ref={flatListRef}
                data={displayData}
                renderItem={renderItem}
                keyExtractor={keyExtractor}
                contentContainerStyle={styles.listContent}
                onEndReached={handleLoadMore}
                onEndReachedThreshold={0.5}
                refreshing={refreshing}
                onRefresh={handleRefresh}
                getItemLayout={getItemLayout}
                ListEmptyComponent={
                  <EmptyListComponent searchQuery={searchQuery} />
                }
                ListFooterComponent={
                  <ListFooter loading={loading && !initialLoading} />
                }
                initialNumToRender={10}
                maxToRenderPerBatch={10}
                windowSize={10}
                removeClippedSubviews={true}
                updateCellsBatchingPeriod={75}
                showsVerticalScrollIndicator={false}
                maintainVisibleContentPosition={{
                  minIndexForVisible: 0,
                }}
              />
            )}
          </View>
        </View>

        {/* Print Label FAB button */}
        <View
          style={[
            styles.bottomButtonContainer,
            {backgroundColor: colors.surface},
          ]}>
          <View
            style={!action ? styles.buttonWrapper : styles.buttonWrapperRow}>
            <View style={styles.buttonContainer}>
              <FAB
                label="Print Label"
                position="bottomRight"
                onPress={() => onPrintLabels()}
              />
            </View>

            <Modal
              animationType="fade"
              transparent
              visible={modalVisible}
              onRequestClose={() => setModalVisible(false)}>
              <View style={styles.modalContainer}>
                <View
                  style={[
                    styles.modalContent,
                    {backgroundColor: colors.surface},
                  ]}>
                  <View style={styles.modalHeader}>
                    <Text style={[styles.modalTitle, {color: colors.text}]}>
                      Enter Qty Of Label
                    </Text>
                    <TouchableOpacity
                      onPress={() => {
                        setModalVisible(false);
                        setModalItemId(null);
                        setModalStockInput('');
                      }}
                      style={styles.modalCloseButton}>
                      <Text
                        style={[
                          styles.modalCloseButtonText,
                          {color: colors.error},
                        ]}>
                        X
                      </Text>
                    </TouchableOpacity>
                  </View>
                  <TextInput
                    style={[
                      styles.modalInput,
                      {
                        backgroundColor: colors.card,
                        borderColor: colors.border,
                        color: colors.text,
                      },
                    ]}
                    placeholder="Enter No of Labels"
                    placeholderTextColor={colors.placeholder}
                    keyboardType="numeric"
                    value={modalStockInput}
                    onChangeText={value =>
                      handleInputChange(value, modalItemId)
                    }
                  />
                  <View style={styles.modalButtonContainer}>
                    <AppButton Title="Done" OnPress={handleDoneClick} />
                  </View>
                </View>
              </View>
            </Modal>

            <Modal
              animationType="fade"
              transparent
              visible={printerConfig}
              onRequestClose={() => setPrinterConfig(false)}>
              <View style={styles.modalContainer}>
                <View
                  style={[
                    styles.modalContent,
                    {backgroundColor: colors.surface},
                  ]}>
                  <View style={styles.modalHeader}>
                    <Text style={[styles.modalTitle, {color: colors.text}]}>
                      Select Printer
                    </Text>
                    <TouchableOpacity
                      onPress={() => setPrinterConfig(false)}
                      style={styles.modalCloseButton}>
                      <Text
                        style={[
                          styles.modalCloseButtonText,
                          {color: colors.error},
                        ]}>
                        X
                      </Text>
                    </TouchableOpacity>
                  </View>
                  <Text
                    style={[
                      styles.selectedPrinterText,
                      {color: colors.success},
                    ]}>
                    Selected Printer#:{' '}
                    {selectedPrinters ? selectedPrinters : 'N/A'}
                  </Text>
                  <View style={styles.printersListContainer}>
                    <ScrollView>
                      {printers.map((printer: Printers) => (
                        <TouchableOpacity
                          key={printer.deviceId}
                          style={[
                            styles.printerItem,
                            {backgroundColor: colors.card},
                          ]}
                          onPress={() => selectPrinter(printer)}>
                          <Text
                            style={[
                              styles.printerItemText,
                              {color: colors.text},
                            ]}>
                            {printer.name}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </ScrollView>
                  </View>
                </View>
              </View>
            </Modal>
          </View>
        </View>

        <View style={styles.filterContainer}>
          <AppFilter
            isVisible={filter}
            setIsVisble={setFilter}
            Department={text => setSelectedDepartment(text)}
            Vedor={text => setSelectedVendor(text)}
            Brand={text => setSelectedBrand(text)}
            Category={text => setSelectedSubCategory(text)}
            EnableFilter={setIsEnableFilter}
            selectedDepartment={selectedDepartment}
            selectedVendor={selectedVendor}
            selectedBrand={selectedBrand}
            selectedSubCategory={selectedSubCategory}
            isEnableFilter={isEnableFilter}
          />
        </View>

        <Modal
          animationType="fade"
          transparent={false}
          visible={camera}
          onRequestClose={() => setCamera(false)}>
          <AppScanner
            codeScanner={codeScanner}
            onClose={() => {
              setCamera(false);
              textInputRef?.current?.focus();
            }}
          />
        </Modal>
      </View>
    </TouchableWithoutFeedback>
  );
};

export default PrintLabel;
