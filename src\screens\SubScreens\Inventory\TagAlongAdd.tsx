import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  Keyboard,
  Alert,
  FlatList,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Platform,
  Modal,
} from 'react-native';
import React, {useCallback, useEffect, useRef, useState, memo} from 'react';

import {Inventory, Inventory_Filter, TagAlong} from '../../../server/types';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {
  GetAllItemsWithFilter,
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
  handleSearch,
  onSearchChange_Common,
  showAlert,
  showAlertOK,
} from '../../../utils/PublicHelper';
import {getInventoryPort} from '../../../server/InstanceTypes';
import {Fonts} from '../../../styles/fonts';
import {MaterialColors} from '../../../constants/MaterialColors';
import Header from '../../../components/Inventory/Header';
import AppFilter from '../../../components/Inventory/AppFilter';
import AppSearchWIthFilter from '../../../components/Inventory/AppSearchWIthFilter';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {createItem} from '../../../server/service';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import LottieView from 'lottie-react-native';
import {useCodeScanner} from 'react-native-vision-camera';
import AppScanner from '../../../components/Inventory/AppScanner';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

// Font sizes consistent with the app
const FontSizes = {
  small: 10,
  medium: 12,
  large: 14,
  xLarge: 16,
  xxLarge: 18,
};

// Memoized Item component for optimal rendering performance

type BarcodeScreenRouteProp = RouteProp<any, 'AddTagAlong'>;
const TagAlongAdd: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  // State for all data and filtered data
  const [masterData, setMasterData] = useState<Inventory_Filter[]>([]);
  const [filteredData, setFilteredData] = useState<Inventory_Filter[]>([]);
  const [displayData, setDisplayData] = useState<Inventory_Filter[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [initialLoading, setInitialLoading] = useState<boolean>(true);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [debouncedQuery, setDebouncedQuery] = useState<string>('');

  // Pagination state
  const [page, setPage] = useState<number>(1);
  const ITEMS_PER_PAGE = 20;
  const [refreshing, setRefreshing] = useState<boolean>(false);

  // Filter state
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [selectedVendor, setSelectedVendor] = useState<string>('');
  const [selectedBrand, setSelectedBrand] = useState<string>('');
  const [selectedSubCategory, setSelectedSubCategory] = useState<string>('');
  const [isEnableFilter, setIsEnableFilter] = useState<boolean>(false);
  const [filter, setFilter] = useState<boolean>(false);
  const [showLookup, setshowLookup] = useState<boolean>(false);
  const [isCreate, setIsCreate] = useState<boolean>(
    route.params?.IsCreate || false,
  );

  const [itemNumber, setItemNumber] = useState<string>(
    route.params?.ItemData || '',
  );

  const textInputRef = useRef<TextInput>(null);
  const flatListRef = useRef<FlatList>(null);

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const InventoryItem = memo(
    ({
      item,
      onPress,
    }: {
      item: Inventory_Filter;
      onPress: (item: Inventory_Filter) => void;
    }) => {
      return (
        <TouchableOpacity style={styles.itemCard} onPress={() => onPress(item)}>
          <View style={styles.itemDetails}>
            <Text style={styles.itemName}>{item.ItemName}</Text>
            <Text style={styles.itemCost}>{`Cost: ${item.Cost?.toFixed(
              2,
            )}`}</Text>
          </View>
          <View style={styles.priceContainer}>
            <Text style={styles.priceText}>${item.Price}</Text>
          </View>
        </TouchableOpacity>
      );
    },
    (prevProps, nextProps) => {
      // Only re-render if the item ID changes
      return prevProps.item.ItemNum === nextProps.item.ItemNum;
    },
  );

  // Empty list component
  const EmptyListComponent = memo(({searchQuery}: {searchQuery: string}) => (
    <View style={styles.emptyContainer}>
      <View style={styles.animationContainer}>
        <LottieView
          style={styles.lottie}
          source={require('../../../assets/Lotties/Nodata.json')}
          autoPlay
          loop
        />
      </View>
      <Text style={styles.emptyText}>
        {searchQuery
          ? 'No matching items found'
          : 'No inventory items available'}
      </Text>
    </View>
  ));

  // Footer loading component
  const ListFooter = memo(({loading}: {loading: boolean}) => {
    if (!loading) return null;

    return (
      <View style={styles.footer}>
        <ActivityIndicator size="small" color={colors.primary} />
        <Text style={styles.footerText}>Loading more items...</Text>
      </View>
    );
  });

  // Initialize data on screen focus
  useFocusEffect(
    useCallback(() => {
      setInitialLoading(true);
      getInventoryData();
      setFilter(false);
      setSearchQuery('');
      setPage(1);
      setshowLookup(false);
      setTimeout(() => {
        if (textInputRef.current) {
          textInputRef.current.blur();
          setTimeout(() => {
            if (textInputRef.current) {
              textInputRef.current.focus();
            }
          }, 50);
        }
      }, 50);
    }, []),
  );

  // Fetch inventory data
  const getInventoryData = async () => {
    try {
      setLoading(true);
      const port = await getInventoryPort();
      GetAllItemsWithFilter(
        port.toString(),
        '/getInventoryFilter',
        data => {
          setMasterData(data);
          setFilteredData(data);
          applyPagination(data, 1);
          setInitialLoading(false);
          setLoading(false);
        },
        // We'll handle displayData ourselves
        () => {},
        setLoading,
        false,
      );
    } catch (error) {
      console.error('Error fetching inventory:', error);
      setInitialLoading(false);
      setLoading(false);
    }
  };

  // Apply search debouncing
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 300);

    return () => {
      clearTimeout(handler);
    };
  }, [searchQuery]);

  // Determine if filters are active
  useEffect(() => {
    if (
      selectedBrand ||
      selectedDepartment ||
      selectedVendor ||
      selectedSubCategory
    ) {
      setIsEnableFilter(true);
    } else {
      setIsEnableFilter(false);
    }
  }, [selectedBrand, selectedDepartment, selectedVendor, selectedSubCategory]);

  // Apply filters and search
  useEffect(() => {
    // Skip if masterData isn't loaded yet
    if (masterData.length === 0) return;

    // Reset pagination when filters change
    setPage(1);

    // Filter by department, vendor, brand, and subcategory
    const filtered = masterData.filter(item => {
      const matchesDepartment = selectedDepartment
        ? item.Dept_ID === selectedDepartment
        : true;
      const matchesVendor = selectedVendor
        ? item.Vendor_Number === selectedVendor
        : true;
      const matchesBrand = selectedBrand ? item.Brand === selectedBrand : true;
      const matchesSubCategory = selectedSubCategory
        ? item.SubCategory === selectedSubCategory
        : true;

      // Also apply search filter if we have a query
      const searchTerm = debouncedQuery.toLowerCase();
      const matchesSearch = !debouncedQuery
        ? true
        : (item.ItemNum?.toString().toLowerCase() || '').includes(searchTerm) ||
          (item.ItemName?.toLowerCase() || '').includes(searchTerm) ||
          (item.ItemDescription?.toLowerCase() || '').includes(searchTerm) ||
          (item.SerialNumber?.toLowerCase() || '').includes(searchTerm);

      return (
        matchesDepartment &&
        matchesVendor &&
        matchesBrand &&
        matchesSubCategory &&
        matchesSearch
      );
    });

    setFilteredData(filtered);
    applyPagination(filtered, 1);
  }, [
    selectedDepartment,
    selectedVendor,
    selectedBrand,
    selectedSubCategory,
    debouncedQuery,
    masterData,
  ]);

  // Pagination function
  const applyPagination = (data: Inventory_Filter[], currentPage: number) => {
    const endIndex = currentPage * ITEMS_PER_PAGE;
    const paginatedItems = data.slice(0, endIndex);
    setDisplayData(paginatedItems);
  };

  // Load more data when reaching the end of the list
  const handleLoadMore = useCallback(() => {
    if (page * ITEMS_PER_PAGE < filteredData.length) {
      const nextPage = page + 1;
      setPage(nextPage);
      applyPagination(filteredData, nextPage);
    }
  }, [page, filteredData, ITEMS_PER_PAGE]);

  // Apply pagination when page changes
  useEffect(() => {
    if (filteredData.length > 0 && page > 1) {
      applyPagination(filteredData, page);
    }
  }, [page, filteredData]);

  // Pull to refresh implementation
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    setPage(1);

    try {
      await getInventoryData();
    } catch (error) {
      console.error('Error refreshing data:', error);
    }

    setRefreshing(false);
  }, []);

  // Add item to tag along functionality
  const addItemsToTagAlong = async (tagAlongItem: Inventory) => {
    if (isCreate) {
      if (route.params?.ItemData === tagAlongItem.ItemNum) {
        showAlertOK(
          `Item Not Longer Available to Add!`,
          'Not A Valid Item',
          'OK',
          () => {
            if (textInputRef.current) {
              textInputRef.current.clear();
              textInputRef.current.blur();
            }

            setSearchQuery('');
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 200);
          },
        );
      }
      try {
        const existingData = await AsyncStorage.getItem('SetTagAlongs');
        const existingArray = existingData ? JSON.parse(existingData) : [];

        const indexToUpdate = existingArray.some(
          item => item.TagAlong_ItemNum === tagAlongItem?.ItemNum,
        );
        if (indexToUpdate) {
          showAlertOK(
            `This Item Already Added to Tag Along!`,
            'Item Already Added',
            'OK',
            () => {
              if (textInputRef.current) {
                textInputRef.current.clear();
                textInputRef.current.blur();
              }

              setSearchQuery('');
              Keyboard.dismiss();
              setTimeout(() => {
                if (textInputRef.current) {
                  textInputRef.current.focus();
                }
              }, 200);
            },
          );
        } else {
          const storeId = await AsyncStorage.getItem('STOREID');
          const ValidStore = storeId === null ? '1001' : storeId;
          const createTag: TagAlong = {
            ItemNum: route.params?.ItemData,
            Quantity: 1,
            Store_ID: ValidStore,
            TagAlong_ItemNum: tagAlongItem?.ItemNum,
          };

          existingArray.push(createTag);
          await AsyncStorage.setItem(
            'SetTagAlongs',
            JSON.stringify(existingArray),
          );
          Alert.alert('Tag Along Added');
          navigation.goBack();
        }
      } catch (error) {
        console.log(error);
      }
    } else {
      if (route.params?.ItemData === tagAlongItem.ItemNum) {
        showAlertOK(
          `Item Not Longer Available to Add!`,
          'Not A Valid Item',
          'OK',
          () => {
            if (textInputRef.current) {
              textInputRef.current.clear();
              textInputRef.current.blur();
            }

            setSearchQuery('');
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 200);
          },
        );
      }

      const getExist = await GetItemsParamsNoFilterNoReturn(
        (await getInventoryPort()).toString(),
        '/checkExistsTagAlong/:ItemNum/:TagAlong_ItemNum',
        {ItemNum: itemNumber, TagAlong_ItemNum: tagAlongItem.ItemNum},
      );
      if (Array.isArray(getExist) && getExist.length === 0) {
        const storeId = await AsyncStorage.getItem('STOREID');
        const ValidStore = storeId === null ? '1001' : storeId;
        const createTag: TagAlong = {
          ItemNum: itemNumber,
          Quantity: 1,
          Store_ID: ValidStore,
          TagAlong_ItemNum: tagAlongItem.ItemNum,
        };

        const result = await createItem(
          (await getInventoryPort()).toString(),
          '/createtagalongs',
          createTag,
        );
        if (result) {
          Alert.alert('Tag Along Added');
          navigation.goBack();
        }
      } else {
        showAlertOK(
          `This Item Already Added to Tag Along!`,
          'Item Already Added',
          'OK',
          () => {
            if (textInputRef.current) {
              textInputRef.current.clear();
              textInputRef.current.blur();
            }

            setSearchQuery('');
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 200);
          },
        );
      }
    }
  };

  // Toggle keyboard/lookup
  const toggleLookup = useCallback((value: boolean) => {
    applyPagination(filteredData, page);
    setshowLookup(value);
    setSearchQuery('');

    if (Platform.OS === 'android') {
      if (value) {
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.blur();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 50);
          }
        }, 50);
      } else {
        Keyboard.dismiss();
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.blur();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 50);
          }
        }, 50);
      }
      return;
    }

    // iOS handling
    if (value) {
      setTimeout(() => {
        textInputRef.current?.focus();
      }, 100);
    } else {
      setSearchQuery('');
      Keyboard.dismiss();
    }
  }, []);
  // Handle search
  const handleSearchChange = async (text: string) => {
    const getBarcode = await GetItemsParamsNoFilterNoReturn<Inventory_Filter[]>(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      {ItemNum: text},
    );
    setSearchQuery(getBarcode[0]?.ItemNum || text);
    setCamera(false);
    if (text.trim() === '') {
      setSearchQuery('');
      setFilteredData(masterData);
      return;
    } else {
      if (showLookup) {
        handleSearch(
          text,
          filteredData,
          ['ItemName', 'ItemNum'],
          setDisplayData,
          setLoading,
        );
      } else {
        if (Array.isArray(getBarcode) && getBarcode.length === 0) {
          const userConfirmed = await showAlert(
            'Item not found. Do you want to create a new item?',
          );
          if (userConfirmed) {
            navigation.navigate('ItemType', {
              ItemData: getBarcode[0]?.ItemNum || text,
            });
          } else {
            if (textInputRef.current) {
              textInputRef.current.clear();
              textInputRef.current.blur();
            }

            setSearchQuery('');
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 200);
          }
        } else {
          handleSearch(
            getBarcode[0]?.ItemNum || text,
            filteredData,
            ['ItemName', 'ItemNum'],
            setDisplayData,
            setLoading,
          );
        }
      }
    }
  };

  // Optimized rendering components
  const renderItem = useCallback(
    ({item}) => <InventoryItem item={item} onPress={addItemsToTagAlong} />,
    [addItemsToTagAlong],
  );

  // Optimized key extractor
  const keyExtractor = useCallback(
    (item: Inventory_Filter) => item.ItemNum.toString(),
    [],
  );

  // GetItemLayout for optimized rendering
  const getItemLayout = useCallback(
    (_, index) => ({
      length: 88, // approximate height of each item
      offset: 88 * index,
      index,
    }),
    [],
  );

  const handleOutsidePress = () => {
    if (showLookup) {
      setshowLookup(false);
      Keyboard.dismiss();
    }
  };

  const handleDoneClick = () => {
    setshowLookup(false);
    if (textInputRef.current) {
      textInputRef.current.clear();
      textInputRef.current.blur();
    }

    setSearchQuery('');
    Keyboard.dismiss();
    setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.focus();
      }
    }, 200);
    applyPagination(filteredData, page);
  };

  const [camera, setCamera] = useState(false);
  const codeScanner = useCodeScanner({
    codeTypes: [
      'qr',
      'ean-13',
      'upc-a',
      'ean-8',
      'upc-e',
      'code-128',
      'code-39',
      'code-93',
    ],
    onCodeScanned: codes => {
      if (codes.length > 0) {
        handleSearchChange(codes[0].value);
      }
    },
  });

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    searchContainer: {
      paddingHorizontal: wp('2.5%'),
      marginBottom: 5,
    },
    itemCountContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginVertical: hp('1%'),
      paddingHorizontal: wp('2%'),
    },
    itemCountText: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestBold,
      color: colors.text,
    },
    listContainer: {
      flex: 1,
      paddingHorizontal: wp('2.5%'),
    },
    listContent: {
      paddingBottom: hp('2%'),
    },
    itemCard: {
      backgroundColor: colors.card,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: wp('4%'),
      paddingVertical: hp('1.8%'),
      marginBottom: hp('1.2%'),
      borderRadius: 12,
      borderLeftColor: colors.primary,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    itemDetails: {
      flex: 1,
      gap: hp('0.5%'),
    },
    itemName: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.text,
    },
    itemCost: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: colors.textSecondary,
    },
    priceContainer: {
      backgroundColor: isDark ? colors.surface : colors.primary + '20', // 20% opacity for light mode
      paddingVertical: hp('0.8%'),
      paddingHorizontal: wp('2%'),
      borderRadius: 8,
      justifyContent: 'center',
      alignItems: 'center',
    },
    priceText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.large,
      color: colors.primary,
    },
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: hp('10%'),
    },
    animationContainer: {
      borderRadius: 20,
      backgroundColor: 'transparent',
    },
    lottie: {
      height: 200,
      width: 200,
      backgroundColor: 'transparent',
    },
    emptyText: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: 20,
      fontFamily: Fonts.OnestMedium,
    },
    footer: {
      padding: 15,
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    footerText: {
      marginLeft: 10,
      fontFamily: Fonts.OnestMedium,
      color: colors.textSecondary,
    },
    centered: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: hp('10%'),
    },
  });

  return (
    <TouchableWithoutFeedback onPress={handleOutsidePress}>
      <View style={styles.container}>
        <Header
          NavName="Link Items"
          isOption={true}
          Options={() => {
            if (textInputRef.current) {
              textInputRef.current.clear();
              textInputRef.current.blur();
            }

            setSearchQuery('');
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 200);

            setCamera(!camera);
          }}
        />

        <View style={styles.searchContainer}>
          <AppSearchWIthFilter
            OnSearch={handleSearchChange}
            SearchValue={searchQuery}
            OnSearchSet={() => setFilter(true)}
            isEnableFilter={isEnableFilter}
            Keyboardon={showLookup}
            textInputRef={textInputRef}
            onToggleLookup={toggleLookup}
            OnSubmitEditing={handleDoneClick}
          />

          <View style={styles.itemCountContainer}>
            <Text style={styles.itemCountText}>
              Total Items: ({filteredData.length || 0})
            </Text>
          </View>
        </View>

        {/* List Container */}
        <View style={styles.listContainer}>
          {initialLoading ? (
            <View style={styles.centered}>
              <ActivityIndicator size="large" color={colors.primary} />
            </View>
          ) : (
            <View style={{height: '84%'}}>
              <FlatList
                ref={flatListRef}
                data={displayData}
                renderItem={renderItem}
                keyExtractor={keyExtractor}
                contentContainerStyle={styles.listContent}
                onEndReached={handleLoadMore}
                onEndReachedThreshold={0.5}
                refreshing={refreshing}
                onRefresh={handleRefresh}
                getItemLayout={getItemLayout}
                ListEmptyComponent={
                  <EmptyListComponent searchQuery={searchQuery} />
                }
                ListFooterComponent={
                  <ListFooter loading={loading && !initialLoading} />
                }
                initialNumToRender={10}
                maxToRenderPerBatch={10}
                windowSize={10}
                removeClippedSubviews={true}
                updateCellsBatchingPeriod={75}
                showsVerticalScrollIndicator={false}
                maintainVisibleContentPosition={{
                  minIndexForVisible: 0,
                }}
              />
            </View>
          )}
        </View>

        {/* Filter Modal */}
        <AppFilter
          isVisible={filter}
          setIsVisble={setFilter}
          Department={text => setSelectedDepartment(text)}
          Vedor={text => setSelectedVendor(text)}
          Brand={text => setSelectedBrand(text)}
          Category={text => setSelectedSubCategory(text)}
          EnableFilter={setIsEnableFilter}
          selectedDepartment={selectedDepartment}
          selectedVendor={selectedVendor}
          selectedBrand={selectedBrand}
          selectedSubCategory={selectedSubCategory}
          isEnableFilter={isEnableFilter}
        />

        <Modal
          animationType="fade"
          transparent={false}
          visible={camera}
          onRequestClose={() => setCamera(false)}>
          <AppScanner
            codeScanner={codeScanner}
            onClose={() => {
              setCamera(false);
              textInputRef?.current?.focus();
            }}
          />
        </Modal>
      </View>
    </TouchableWithoutFeedback>
  );
};

export default TagAlongAdd;
