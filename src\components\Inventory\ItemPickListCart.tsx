import {View, Text, TouchableOpacity} from 'react-native';
import React from 'react';
import {Primary, Secondary, SecondaryHint} from '../../constants/Color';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../styles/fonts';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {MaterialColors} from '../../constants/MaterialColors';
import {useThemeColors} from '../../Theme/useThemeColors';

type Props = {
  Reference: string | null;
  CreatedDate: string;
  OnPress: () => void;
  OnPressDelete?: () => void;
  GrandTotal: number;
};

const ItemPickListCart = ({
  Reference,
  CreatedDate,
  OnPress,
  OnPressDelete,
  GrandTotal,
}: Props) => {
  const colors = useThemeColors();

  return (
    <TouchableOpacity
      style={{
        borderRadius: 8,
        paddingHorizontal: wp('1.8%'),
        paddingVertical: hp('0.8%'),
        marginTop: 6,
        backgroundColor: colors.card, // Changed from MaterialColors.surface
        borderBottomWidth: 1,
        borderBottomColor: colors.border, // Changed from MaterialColors.grey[200]
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        flex: 1,
      }}
      onPress={OnPress}>
      <View style={{gap: 3, width: wp('70%')}}>
        <Text
          numberOfLines={1}
          ellipsizeMode="tail"
          style={{
            fontSize: FontSizes.medium,
            fontFamily: Fonts.OnestBold,
            color: colors.text, // Add this line
          }}>
          Refrence Number: {Reference}
        </Text>
        <Text
          style={{
            fontSize: FontSizes.medium,
            fontFamily: Fonts.OnestMedium,
            color: colors.textSecondary, // Changed from '#A1A1A1'
          }}>
          Date: {CreatedDate}
        </Text>
        <Text
          style={{
            fontSize: FontSizes.large,
            fontFamily: Fonts.OnestBold,
            color: colors.primary, // Changed from Primary
          }}>
          Total: {GrandTotal > 0 ? `$${GrandTotal}` : '$0'}
        </Text>
      </View>

      <TouchableOpacity onPress={OnPressDelete}>
        <MaterialCommunityIcons
          name="delete-outline"
          color={colors.error} // Changed from MaterialColors.error.main
          size={25}
        />
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

export default ItemPickListCart;
