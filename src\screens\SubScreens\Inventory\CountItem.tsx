import {
  View,
  Text,
  StyleSheet,
  TextInput,
  FlatList,
  ActivityIndicator,
  Alert,
  TouchableOpacity,
  Modal,
  Keyboard,
  TouchableWithoutFeedback,
  Dimensions,
  Platform,
} from 'react-native';
import React, {useCallback, useEffect, useRef, useState, memo} from 'react';
import {
  Backround,
  Primary,
  Secondary,
  SecondaryHint,
} from '../../../constants/Color';
import Header from '../../../components/Inventory/Header';
import AppDropDown from '../../../components/Inventory/AppDropDown';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import AppButton from '../../../components/Inventory/AppButton';
import {
  RouteProp,
  useFocusEffect,
  useIsFocused,
  CommonActions,
} from '@react-navigation/native';
import {
  BrandOrSubCategory,
  Brands,
  Department,
  Inventory,
  Inventory_Filter,
  SubCategories,
  Vendor,
} from '../../../server/types';
import Search from '../../../components/Inventory/Search';
import {
  GetAllItems,
  GetAllItemsNoProps,
  GetAllItemsWithFilter,
  GetItemsParamsNoFilterNoReturn,
  handleSearch,
  onSearchChange_Common,
  showAlert,
  showAlertOK,
  updateData,
} from '../../../utils/PublicHelper';
import {updateItem} from '../../../server/service';
import DataList from '../../../components/Inventory/AppList';
import {getInventoryPort} from '../../../server/InstanceTypes';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../../styles/fonts';
import AppFilter from '../../../components/Inventory/AppFilter';
import AppSearchWIthFilter from '../../../components/Inventory/AppSearchWIthFilter';
import CustomCheckbox from '../../../components/Inventory/CustomCheckbox';
import {applyDefaults} from '../../../Validator/Inventory/Barcode';
import {MaterialColors} from '../../../constants/MaterialColors';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import FAB from '../../../components/common/FAB';
import LottieView from 'lottie-react-native';
import AppLoader from '../../../components/Inventory/AppLoader';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AppScanner from '../../../components/Inventory/AppScanner';
import {useCodeScanner} from 'react-native-vision-camera';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

const {width, height} = Dimensions.get('window');

// Memoized Item component for optimal rendering performance

type CountItemRouteProp = RouteProp<any, 'AdjustStock'>;

const CountItem: React.FC<{route: CountItemRouteProp; navigation: any}> = ({
  route,
  navigation,
}) => {
  // State for all data and filtered data
  const [masterData, setMasterData] = useState<Inventory_Filter[]>([]);
  const [filteredData, setFilteredData] = useState<Inventory_Filter[]>([]);
  const [displayData, setDisplayData] = useState<Inventory_Filter[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [initialLoading, setInitialLoading] = useState<boolean>(true);
  const [appLoader, setAppLoader] = useState<boolean>(true);

  // Count item specific state
  const [action, setAction] = useState<boolean>(false);
  const [inputValues, setInputValues] = useState<
    {itemNum: string; value: string}[]
  >([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState<string>('');
  const textInputRef = useRef<TextInput>(null);
  const flatListRef = useRef<FlatList>(null);

  // Modal state
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [modalItemId, setModalItemId] = useState<string | null>(null);
  const [modalStockInput, setModalStockInput] = useState<string>('');
  const [newStockValues, setNewStockValues] = useState<{[key: string]: string}>(
    {},
  );

  // Pagination state
  const [page, setPage] = useState<number>(1);
  const ITEMS_PER_PAGE = 20;
  const [refreshing, setRefreshing] = useState<boolean>(false);

  // Filter state
  const [departments, setDepartments] = useState<Department[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [selectedVendor, setSelectedVendor] = useState<string>('');
  const [selectedBrand, setSelectedBrand] = useState<string>('');
  const [selectedSubCategory, setSelectedSubCategory] = useState<string>('');
  const [brandorSubCat, SetBrandSubOrCat] = useState<BrandOrSubCategory[]>([]);
  const [getBrands, setGetBrands] = useState<Brands[]>([]);
  const [getSubCategories, setGetSubCategories] = useState<SubCategories[]>([]);
  const [isEnableFilter, setIsEnableFilter] = useState<boolean>(false);
  const [showLookup, setshowLookup] = useState<boolean>(false);
  const [filter, setFilter] = useState<boolean>(false);

  const actionRef = useRef(false);

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const CountInventoryItem = memo(
    ({
      item,
      onPress,
      inputValue,
      hasInputValue,
    }: {
      item: Inventory_Filter;
      onPress: () => void;
      inputValue: string;
      hasInputValue: boolean;
    }) => {
      return (
        <TouchableOpacity
          style={[
            styles.row,
            hasInputValue && {borderLeftWidth: 2, borderLeftColor: Primary},
          ]}
          onPress={onPress}>
          <View style={styles.leftContent}>
            <View style={styles.iconContainer}>
              <MaterialCommunityIcons
                name="package-variant"
                color={MaterialColors.primary.main}
                size={hp('1.9%')}
              />
            </View>
            <View style={styles.itemDetails}>
              <Text style={styles.itemName}>{item.ItemName}</Text>
              <View style={styles.metadataRow}>
                <View style={styles.infoGroup}>
                  <MaterialCommunityIcons
                    name="warehouse"
                    color={Primary}
                    size={hp('1.6%')}
                  />
                  {!hasInputValue ? (
                    <Text style={styles.stockText}>
                      {item.In_Stock} in Stock
                    </Text>
                  ) : (
                    <>
                      {inputValue !== '' ? (
                        <Text style={styles.stockText}>
                          {`${item.In_Stock} → ${inputValue}`}
                        </Text>
                      ) : (
                        <Text style={styles.stockText}>
                          {item.In_Stock} in Stock
                        </Text>
                      )}
                    </>
                  )}
                </View>
                <View style={styles.infoGroup}>
                  <MaterialCommunityIcons
                    name="barcode"
                    color="#A1A1A1"
                    size={hp('1.6%')}
                  />
                  <Text style={styles.barcodeText}>{item.ItemNum}</Text>
                </View>
              </View>
            </View>
          </View>

          <View style={styles.priceContainer}>
            <Text style={styles.priceText}>${item.Price}</Text>
          </View>
        </TouchableOpacity>
      );
    },
    (prevProps, nextProps) => {
      // Only re-render if essential props change
      return (
        prevProps.item.ItemNum === nextProps.item.ItemNum &&
        prevProps.inputValue === nextProps.inputValue &&
        prevProps.hasInputValue === nextProps.hasInputValue
      );
    },
  );

  // Empty list component
  const EmptyListComponent = memo(({searchQuery}: {searchQuery: string}) => (
    <View style={styles.emptyContainer}>
      <View style={styles.animationContainer}>
        <LottieView
          style={styles.lottie}
          source={require('../../../assets/Lotties/Nodata.json')}
          autoPlay
          loop
        />
      </View>
      <Text style={styles.emptyText}>
        {searchQuery
          ? 'No matching items found'
          : 'No inventory items available'}
      </Text>
    </View>
  ));

  // Footer loading component
  const ListFooter = memo(({loading}: {loading: boolean}) => {
    if (!loading) return null;
    const [appLoader, setAppLoader] = useState<boolean>(true);

    return (
      <View style={styles.footer}>
        <AppLoader
          modalVisible={appLoader}
          setModalVisible={setAppLoader}
          isLookup={true}
        />
        <Text style={styles.footerText}>Loading more items...</Text>
      </View>
    );
  });

  // Initialize data on screen focus
  useFocusEffect(
    useCallback(() => {
      setInitialLoading(true);
      invNoPagination();
      setFilter(false);
      setSearchQuery('');
      setPage(1);
      setshowLookup(false);

      // Focus the text input
      setTimeout(() => {
        if (textInputRef.current) {
          textInputRef.current.blur();
          setTimeout(() => {
            if (textInputRef.current) {
              textInputRef.current.focus();
            }
          }, 50);
        }
      }, 50);
    }, []),
  );

  // Apply search debouncing
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 300);

    return () => {
      clearTimeout(handler);
    };
  }, [searchQuery]);

  // Check if filters are enabled
  useEffect(() => {
    if (
      selectedBrand ||
      selectedDepartment ||
      selectedVendor ||
      selectedSubCategory
    ) {
      setIsEnableFilter(true);
    } else {
      setIsEnableFilter(false);
    }
  }, [selectedBrand, selectedDepartment, selectedVendor, selectedSubCategory]);

  // Apply filters and search
  useEffect(() => {
    // Skip if masterData isn't loaded yet
    if (masterData.length === 0) return;

    // Reset pagination when filters change
    setPage(1);

    // Filter by department, vendor, brand, and subcategory
    const filtered = masterData.filter(item => {
      const matchesDepartment = selectedDepartment
        ? item.Dept_ID === selectedDepartment
        : true;
      const matchesVendor = selectedVendor
        ? item.Vendor_Number === selectedVendor
        : true;
      const matchesBrand = selectedBrand ? item.Brand === selectedBrand : true;
      const matchesSubCategory = selectedSubCategory
        ? item.SubCategory === selectedSubCategory
        : true;

      // Also apply search filter if we have a query
      const searchTerm = debouncedQuery.toLowerCase();
      const matchesSearch = !debouncedQuery
        ? true
        : (item.ItemNum?.toString().toLowerCase() || '').includes(searchTerm) ||
          (item.ItemName?.toLowerCase() || '').includes(searchTerm);

      return (
        matchesDepartment &&
        matchesVendor &&
        matchesBrand &&
        matchesSubCategory &&
        matchesSearch
      );
    });

    // Sort data - put items with input values first
    const sortedFiltered = [...filtered].sort((a, b) => {
      const aHasInput = inputValues.some(
        input => input.itemNum === a.ItemNum && input.value,
      );
      const bHasInput = inputValues.some(
        input => input.itemNum === b.ItemNum && input.value,
      );

      if (aHasInput && !bHasInput) return -1; // a comes first
      if (!aHasInput && bHasInput) return 1; // b comes first
      return 0; // keep original order if both have or don't have inputs
    });

    setFilteredData(sortedFiltered);
    applyPagination(sortedFiltered, 1);
  }, [
    selectedDepartment,
    selectedVendor,
    selectedBrand,
    selectedSubCategory,
    debouncedQuery,
    masterData,
    inputValues, // Add inputValues to ensure re-sort when values change
  ]);

  // Pagination function
  const applyPagination = (data: Inventory_Filter[], currentPage: number) => {
    const endIndex = currentPage * ITEMS_PER_PAGE;
    const paginatedItems = data.slice(0, endIndex);
    setDisplayData(paginatedItems);
  };

  // Load more data when reaching the end of the list
  const handleLoadMore = useCallback(() => {
    if (page * ITEMS_PER_PAGE < filteredData.length) {
      const nextPage = page + 1;
      setPage(nextPage);
      applyPagination(filteredData, nextPage);
    }
  }, [page, filteredData, ITEMS_PER_PAGE]);

  // Apply pagination when page changes
  useEffect(() => {
    if (filteredData.length > 0 && page > 1) {
      applyPagination(filteredData, page);
    }
  }, [page, filteredData]);

  // Fetch inventory data
  const invNoPagination = async () => {
    try {
      setLoading(true);
      const port = await getInventoryPort();
      const result = await GetAllItemsNoProps(port, '/getInventoryFilter');
      if (result === undefined || !result) {
        showAlertOK(
          'Database Connection Falied, Please Check Your Database Configuration',
          'Connection Failed',
          'OK',
        );
        return;
      }
      setMasterData(result);
      setFilteredData(result);
      applyPagination(result, 1);
      setInitialLoading(false);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching inventory:', error);
      setInitialLoading(false);
      setLoading(false);
    }
  };

  // Pull to refresh implementation
  const handleRefresh = async () => {
    setRefreshing(true);
    setPage(1);

    try {
      await invNoPagination();
    } catch (error) {
      console.error('Error refreshing data:', error);
    }

    setRefreshing(false);
  };

  const SavedPhysicalCount = async () => {
    // Toggle action state and update ref immediately
    const newActionState = !action;
    setAction(newActionState);
    actionRef.current = newActionState; // Update ref immediately

    if (newActionState) {
      // Just started the physical count
      showAlertOK(`Physical Count Started`, 'Physical Count');
      return;
    }

    // Finishing the physical count
    const Remaining = Number(filteredData.length) - Number(inputValues.length);
    showAlert(
      `You've counted ${inputValues.length} out of ${filteredData.length} items. What to do with the remaining ${Remaining} items?`,
      'Confirmation',
      true,
      'Change Stock to 0',
      'Keep Current Quantity',
    )
      .then(async result => {
        if (result) {
          inputValues.map(async countUpdate => {
            const getBarcode = await GetItemsParamsNoFilterNoReturn(
              (await getInventoryPort()).toString(),
              '/inventory/:ItemNum',
              {ItemNum: countUpdate.itemNum},
            );
            const inventoryData: Partial<Inventory> = {
              ItemNum: countUpdate.itemNum,
              ItemName: getBarcode[0]?.ItemName,
              Dept_ID: getBarcode[0]?.Dept_ID,
              Cost: getBarcode[0]?.Cost,
              Price: getBarcode[0]?.Price,
              Retail_Price: getBarcode[0]?.Retail_Price,
              In_Stock: Number(countUpdate.value),
              Date_Created: getBarcode[0]?.Date_Created,
              Last_Sold: getBarcode[0]?.Last_Sold,
              Location: getBarcode[0]?.Location,
              Vendor_Number: getBarcode[0]?.Vendor_Number,
              Vendor_Part_Num: getBarcode[0]?.Vendor_Part_Num,
              Reorder_Level: getBarcode[0]?.Reorder_Level,
              Reorder_Quantity: getBarcode[0]?.Reorder_Quantity,
              ReOrder_Cost: getBarcode[0]?.ReOrder_Cost,
              Unit_Size: getBarcode[0]?.Unit_Size,
              Unit_Type: getBarcode[0]?.Unit_Type,
              FoodStampable: getBarcode[0]?.FoodStampable,
              Tax_1: getBarcode[0]?.Tax_1[0],
              Tax_2: getBarcode[0]?.Tax_2[0],
              Tax_3: getBarcode[0]?.Tax_3[0],
              Tax_4: getBarcode[0]?.Tax_4[0],
              Tax_5: getBarcode[0]?.Tax_5[0],
              Tax_6: getBarcode[0]?.Tax_6[0],
              Check_ID: getBarcode[0]?.Check_ID,
              Check_ID2: getBarcode[0]?.Check_ID2,
              Store_ID: getBarcode[0]?.Store_ID,
              ItemName_Extra: getBarcode[0]?.ItemName_Extra,
            };
            const applyDefault = applyDefaults(inventoryData);
            const result = await updateData<Inventory>({
              baseURL: (await getInventoryPort()).toString(),
              data: applyDefault,
              endpoint: '/updatebarcode',
            });
            setInputValues([]);
          });
        } else {
          const result = await updateItem(
            (await getInventoryPort()).toString(),
            '/updatecountitem',
            {ItemData: inputValues},
          );
          setInputValues([]);
        }
      })
      .catch(error => {
        console.error('Error showing alert', error);
      });
  };

  // Update the openModalForItem function to use the ref
  const openModalForItem = useCallback(
    (itemNum: string) => {
      if (!actionRef.current) {
        // Check the ref instead of the state
        showAlertOK(
          `To proceed, please initialize the physical count by clicking 'Start Physical Count'`,
          'Physical Count Not Started',
        );
      } else {
        setModalItemId(itemNum);
        setModalStockInput(newStockValues[itemNum] || '');
        setModalVisible(true);
      }
    },
    [newStockValues], // Remove action from dependencies since we're using the ref
  );

  const renderItem = useCallback(
    ({item}) => {
      const inputValue =
        inputValues.find(input => input.itemNum === item.ItemNum)?.value || '';
      const hasInputValue = inputValue !== '';

      return (
        <TouchableWithoutFeedback onPress={handleOutsidePress}>
          <CountInventoryItem
            item={item}
            onPress={async () => {
              const lotteryDepartment = await AsyncStorage.getItem(
                'LOTTERY_DEP_ID',
              );
              if (lotteryDepartment) {
                if (lotteryDepartment === item?.Dept_ID) {
                  showAlertOK(
                    `This item cannot be counted at the moment due to system restrictions or configuration settings. Please contact support if you believe this is an error`,
                    'Not Able to Count',
                    'OK',
                  );
                } else {
                  console.log('LOGGING', action);
                  if (!action) {
                    showAlertOK(
                      `To proceed, please initialize the physical count by clicking 'Start Physical Count'`,
                      'Physical Count Not Started',
                    );
                    return;
                  } else {
                    openModalForItem(item.ItemNum);
                  }
                }
              } else {
                if (!action) {
                  showAlertOK(
                    `To proceed, please initialize the physical count by clicking 'Start Physical Count'`,
                    'Physical Count Not Started',
                  );
                  return;
                } else {
                  openModalForItem(item.ItemNum);
                }
              }
            }}
            inputValue={inputValue}
            hasInputValue={actionRef.current && hasInputValue} // Use ref here instead of state
          />
        </TouchableWithoutFeedback>
      );
    },
    [openModalForItem, inputValues, handleOutsidePress, action], // Remove action from dependencies
  );

  // Handle input change
  const handleInputChange = (value: string, itemNum: string) => {
    setInputValues(prevValues => {
      // Check if the item already exists in the array
      const existingItemIndex = prevValues.findIndex(
        item => item.itemNum === itemNum,
      );

      if (existingItemIndex !== -1) {
        // Update the value of the existing item
        const updatedValues = [...prevValues];
        updatedValues[existingItemIndex].value = value;
        return updatedValues;
      } else {
        // Add a new object if the item is not found
        return [...prevValues, {itemNum, value}];
      }
    });

    setModalStockInput(value);
  };

  // Handle done click on modal
  const handleDoneClick = useCallback(() => {
    setSearchQuery('');

    if (modalItemId) {
      setNewStockValues(prev => ({
        ...prev,
        [modalItemId]: modalStockInput,
      }));

      // Update In_Stock in masterData, filteredData, and displayData
      setMasterData(prev =>
        prev.map(item =>
          item.ItemNum === modalItemId
            ? {...item, In_Stock: modalStockInput}
            : item,
        ),
      );
      setFilteredData(prev =>
        prev.map(item =>
          item.ItemNum === modalItemId
            ? {...item, In_Stock: modalStockInput}
            : item,
        ),
      );
      setDisplayData(prev =>
        prev.map(item =>
          item.ItemNum === modalItemId
            ? {...item, In_Stock: modalStockInput}
            : item,
        ),
      );
    }

    setModalVisible(false);
    setModalItemId(null);
    setModalStockInput('');
    if (textInputRef.current) {
      textInputRef.current.clear();
      textInputRef.current.blur();
    }

    setSearchQuery('');
    setshowLookup(false);
    Keyboard.dismiss();
    setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.focus();
      }
    }, 200);
  }, [modalItemId, modalStockInput]);

  // Clear selected items
  const clearSelectedItems = useCallback(() => {
    setInputValues([]);
    setNewStockValues({});
    setModalStockInput('');
    setModalItemId(null);
    setModalVisible(false);
    setAction(false);
    if (textInputRef.current) {
      textInputRef.current.clear();
      textInputRef.current.blur();
    }

    setSearchQuery('');
    setshowLookup(false);
    Keyboard.dismiss();
    setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.focus();
      }
    }, 200);
  }, []);

  // Save physical count Old

  // Handle search change
  const onSearchChange = async (text: string) => {
    setCamera(false);
    const getBarcode = await GetItemsParamsNoFilterNoReturn(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      {ItemNum: text},
    );
    setSearchQuery(getBarcode[0]?.ItemNum || text);

    if (text) {
      if (showLookup) {
        handleSearch(
          text,
          masterData,
          ['ItemName', 'ItemNum'],
          setFilteredData,
          setLoading,
        );
      } else {
        if (Array.isArray(getBarcode) && getBarcode.length === 0) {
          const userConfirmed = await showAlert(
            'Item not found. Do you want to create a new item?',
          );
          if (userConfirmed) {
            navigation.navigate('ItemType', {ItemData: text});
          } else {
            if (textInputRef.current) {
              textInputRef.current.clear();
              textInputRef.current.blur();
            }

            setSearchQuery('');
            setshowLookup(false);
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 200);
            applyPagination(filteredData, page);
          }
        } else {
          const lotteryDepartment = await AsyncStorage.getItem(
            'LOTTERY_DEP_ID',
          );
          if (lotteryDepartment) {
            if (lotteryDepartment === getBarcode[0]?.Dept_ID) {
              showAlertOK(
                `This item cannot be counted at the moment due to system restrictions or configuration settings. Please contact support if you believe this is an error`,
                'Not Able to Count',
                'OK',
                () => {
                  if (textInputRef.current) {
                    textInputRef.current.clear();
                    textInputRef.current.blur();
                  }

                  setSearchQuery('');
                  setshowLookup(false);
                  Keyboard.dismiss();
                  setTimeout(() => {
                    if (textInputRef.current) {
                      textInputRef.current.focus();
                    }
                  }, 200);
                },
              );
            } else {
              if (!action) {
                showAlertOK(
                  `To proceed, please initialize the physical count by clicking 'Start Physical Count'`,
                  'Physical Count Not Started',
                  'OK',
                  () => {
                    if (textInputRef.current) {
                      textInputRef.current.clear();
                      textInputRef.current.blur();
                    }

                    setSearchQuery('');
                    setshowLookup(false);
                    Keyboard.dismiss();
                    setTimeout(() => {
                      if (textInputRef.current) {
                        textInputRef.current.focus();
                      }
                    }, 200);
                  },
                );
                return;
              } else {
                handleSearch(
                  getBarcode[0]?.ItemNum || text,
                  masterData,
                  ['ItemName', 'ItemNum'],
                  setFilteredData,
                  setLoading,
                );
                openModalForItem(getBarcode[0]?.ItemNum || text);
              }
            }
          } else {
            if (!action) {
              showAlertOK(
                `To proceed, please initialize the physical count by clicking 'Start Physical Count'`,
                'Physical Count Not Started',
                'OK',
                () => {
                  if (textInputRef.current) {
                    textInputRef.current.clear();
                    textInputRef.current.blur();
                  }

                  setSearchQuery('');
                  setshowLookup(false);
                  Keyboard.dismiss();
                  setTimeout(() => {
                    if (textInputRef.current) {
                      textInputRef.current.focus();
                    }
                  }, 200);
                },
              );
              return;
            } else {
              handleSearch(
                getBarcode[0]?.ItemNum || text,
                masterData,
                ['ItemName', 'ItemNum'],
                setFilteredData,
                setLoading,
              );
              openModalForItem(getBarcode[0]?.ItemNum || text);
            }
          }
        }
      }
    } else {
      setSearchQuery('');
      setDisplayData(masterData);
      applyPagination(masterData, 1);
    }
  };

  // Toggle lookup mode
  const toggleLookup = useCallback((checked: boolean) => {
    applyPagination(filteredData, page);
    setshowLookup(checked);
    setSearchQuery('');

    if (Platform.OS === 'android') {
      if (checked) {
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.blur();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 50);
          }
        }, 50);
      } else {
        Keyboard.dismiss();
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.blur();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 50);
          }
        }, 50);
      }
      return;
    }

    // iOS handling
    if (checked) {
      setTimeout(() => {
        textInputRef.current?.focus();
      }, 100);
    } else {
      setSearchQuery('');
      Keyboard.dismiss();
    }
  }, []);

  // Hide keyboard when tapping outside
  const handleOutsidePress = useCallback(() => {
    if (showLookup) {
      // Only trigger when keyboard is ON
      setshowLookup(false);
      Keyboard.dismiss();
    }
  }, [showLookup]);

  // Optimized renderItem function

  // Optimized key extractor
  const keyExtractor = useCallback(
    (item: Inventory_Filter) => item.ItemNum.toString(),
    [],
  );

  // GetItemLayout for optimized rendering
  const getItemLayout = useCallback(
    (_, index) => ({
      length: 88, // approximate height of item
      offset: 88 * index,
      index,
    }),
    [],
  );

  const handleDoneClickSub = () => {
    if (textInputRef.current) {
      textInputRef.current.clear();
      textInputRef.current.blur();
    }

    setSearchQuery('');
    setshowLookup(false);
    Keyboard.dismiss();
    setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.focus();
      }
    }, 200);
    applyPagination(filteredData, page);
  };

  const [camera, setCamera] = useState(false);
  const codeScanner = useCodeScanner({
    codeTypes: [
      'qr',
      'ean-13',
      'upc-a',
      'ean-8',
      'upc-e',
      'code-128',
      'code-39',
      'code-93',
    ],
    onCodeScanned: codes => {
      if (codes.length > 0) {
        onSearchChange(codes[0].value);
      }
    },
  });

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    searchContainer: {
      paddingHorizontal: wp('2.5%'),
      marginBottom: 5,
    },
    headerRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: wp('4%'),
      marginBottom: 5,
    },
    headerTitleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 12,
    },
    headerTitle: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestBold,
      color: colors.text,
    },
    itemCount: {
      fontFamily: Fonts.OnestMedium,
      fontSize: FontSizes.medium,
      color: colors.textSecondary,
    },
    listContainer: {
      flex: 1,
      paddingHorizontal: wp('2.5%'),
    },
    listContent: {
      paddingBottom: hp('10%'), // Add space for FAB
    },
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: hp('10%'),
    },
    animationContainer: {
      borderRadius: 20,
      backgroundColor: 'transparent',
    },
    lottie: {
      height: 200,
      width: 200,
      backgroundColor: 'transparent',
    },
    emptyText: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: 20,
      fontFamily: Fonts.OnestMedium,
    },
    footer: {
      padding: 15,
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    footerText: {
      marginLeft: 10,
      fontFamily: Fonts.OnestMedium,
      color: colors.textSecondary,
    },
    centered: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: hp('10%'),
    },
    fabContainer: {
      backgroundColor: colors.surface,
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('0.5%'),
      position: 'absolute',
      left: 0,
      right: 0,
      bottom: 0,
      elevation: 12,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: -4},
      shadowOpacity: isDark ? 0.3 : 0.15,
      shadowRadius: 6,
      borderTopWidth: 0,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      height: hp('8%'),
    },
    row: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: hp('1%'),
      paddingHorizontal: wp('2%'),
      borderBottomWidth: 0.5,
      borderBottomColor: colors.border,
      backgroundColor: colors.card,
      borderRadius: 6,
      marginBottom: 4,
      justifyContent: 'space-between',
    },
    leftContent: {
      flexDirection: 'row',
      alignItems: 'center',
      width: wp('70%'),
    },
    iconContainer: {
      backgroundColor: colors.primary + '30',
      paddingHorizontal: 5,
      paddingVertical: 5,
      borderRadius: 30,
    },
    itemDetails: {
      gap: 2,
      marginLeft: 8,
    },
    itemName: {
      fontSize: FontSizes.small,
      fontFamily: Fonts.OnestBold,
      color: colors.text,
    },
    metadataRow: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    infoGroup: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: 10,
    },
    stockText: {
      fontSize: FontSizes.small || FontSizes.small,
      fontFamily: Fonts.OnestMedium,
      color: colors.primary,
      marginLeft: 3,
    },
    barcodeText: {
      fontSize: FontSizes.small || FontSizes.small,
      fontFamily: Fonts.OnestBold,
      color: colors.textSecondary,
      marginLeft: 3,
    },
    priceContainer: {
      backgroundColor: colors.primary + '30',
      paddingHorizontal: 10,
      paddingVertical: 5,
      borderRadius: 10,
    },
    priceText: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestBold,
      color: colors.primary,
    },
    modalContainer: {
      flex: 1,
      backgroundColor: 'rgba(0,0,0,0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
      backgroundColor: colors.card,
      width: '90%',
      padding: 20,
      borderRadius: 10,
      alignItems: 'center',
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: isDark ? 0.3 : 0.2,
      shadowRadius: 2,
      elevation: 2,
    },
    modalTitle: {
      fontFamily: Fonts.OnestBold,
      fontSize: hp('2.5%'),
      marginBottom: 10,
      color: colors.text,
    },
    modalInput: {
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 5,
      width: '100%',
      height: 40,
      paddingHorizontal: 10,
      marginBottom: 20,
      backgroundColor: colors.surface,
      color: colors.text,
    },
    modalCloseButton: {
      padding: 5,
    },
    modalCloseButtonText: {
      fontSize: hp('2.5%'),
      color: colors.primary,
    },
    modalHeader: {
      flexDirection: 'row',
      width: '100%',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 10,
    },
  });
  return (
    <TouchableWithoutFeedback onPress={handleOutsidePress}>
      <View style={styles.container}>
        {/* Header Section */}
        <Header
          NavName="Count Items"
          isOption={true}
          Options={() => {
            if (textInputRef.current) {
              textInputRef.current.clear();
              textInputRef.current.blur();
            }

            setSearchQuery('');
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 200);

            setCamera(!camera);
          }}
        />

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <AppSearchWIthFilter
            OnSearch={onSearchChange}
            SearchValue={searchQuery}
            OnSearchSet={() => setFilter(true)}
            isEnableFilter={isEnableFilter}
            Keyboardon={showLookup}
            textInputRef={textInputRef}
            onToggleLookup={toggleLookup}
            OnSubmitEditing={handleDoneClickSub}
          />
        </View>

        {/* Available Items Header */}
        <View style={styles.headerRow}>
          <View style={styles.headerTitleContainer}>
            <Text style={styles.headerTitle}>Available Items</Text>
            <Text style={styles.itemCount}>{` (${filteredData.length})`}</Text>
          </View>
        </View>

        {/* List Container */}
        <View style={styles.listContainer}>
          {initialLoading ? (
            <View style={styles.centered}>
              <AppLoader
                modalVisible={appLoader}
                setModalVisible={setAppLoader}
                isLookup={true}
              />
            </View>
          ) : (
            <FlatList
              ref={flatListRef}
              data={displayData}
              renderItem={renderItem}
              keyExtractor={keyExtractor}
              contentContainerStyle={styles.listContent}
              onEndReached={handleLoadMore}
              onEndReachedThreshold={0.5}
              refreshing={refreshing}
              onRefresh={handleRefresh}
              getItemLayout={getItemLayout}
              ListEmptyComponent={
                <EmptyListComponent searchQuery={searchQuery} />
              }
              ListFooterComponent={
                <ListFooter loading={loading && !initialLoading} />
              }
              initialNumToRender={10}
              maxToRenderPerBatch={10}
              windowSize={10}
              removeClippedSubviews={true}
              updateCellsBatchingPeriod={75}
              showsVerticalScrollIndicator={false}
              maintainVisibleContentPosition={{
                minIndexForVisible: 0,
              }}
            />
          )}
        </View>

        {/* FAB and Action Buttons */}
        <View style={styles.fabContainer}>
          <View
            style={
              !action
                ? {height: '100%', width: '100%'}
                : {
                    height: '100%',
                    width: '100%',
                    flexDirection: 'row',
                    alignItems: 'center',
                  }
            }>
            {action && (
              <FAB
                label={'Cancel'}
                position="bottomRightEven"
                style={{
                  backgroundColor: MaterialColors.error.main,
                }}
                onPress={() => {
                  clearSelectedItems();
                  setAction(false);
                }}
              />
            )}

            <FAB
              label={!action ? 'Start Physical Count' : 'Finish'}
              position="bottomRight"
              onPress={SavedPhysicalCount}
            />
          </View>
        </View>

        {/* Modal for Stock Input */}
        <Modal
          animationType="slide"
          transparent
          visible={modalVisible}
          onRequestClose={() => setModalVisible(false)}>
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Enter New Stock</Text>
                <TouchableOpacity
                  onPress={() => {
                    setModalVisible(false);
                    setModalItemId(null);
                    setModalStockInput('');
                  }}
                  style={styles.modalCloseButton}>
                  <Text style={styles.modalCloseButtonText}>X</Text>
                </TouchableOpacity>
              </View>
              <TextInput
                style={styles.modalInput}
                placeholder="New Stock"
                keyboardType="numeric"
                value={modalStockInput}
                onChangeText={value => handleInputChange(value, modalItemId)}
              />
              <View style={{width: '45%'}}>
                <AppButton Title="Done" OnPress={handleDoneClick} />
              </View>
            </View>
          </View>
        </Modal>

        {/* Filter Modal */}
        <AppFilter
          isVisible={filter}
          setIsVisble={setFilter}
          Department={text => setSelectedDepartment(text)}
          Vedor={text => setSelectedVendor(text)}
          Brand={text => setSelectedBrand(text)}
          Category={text => setSelectedSubCategory(text)}
          EnableFilter={setIsEnableFilter}
          selectedDepartment={selectedDepartment}
          selectedVendor={selectedVendor}
          selectedBrand={selectedBrand}
          selectedSubCategory={selectedSubCategory}
          isEnableFilter={isEnableFilter}
        />

        <Modal
          animationType="fade"
          transparent={false}
          visible={camera}
          onRequestClose={() => setCamera(false)}>
          <AppScanner
            codeScanner={codeScanner}
            onClose={() => {
              setCamera(false);
              textInputRef?.current?.focus();
            }}
          />
        </Modal>
      </View>
    </TouchableWithoutFeedback>
  );
};

export default CountItem;
