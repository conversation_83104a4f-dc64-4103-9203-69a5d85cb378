import {View, Text, ScrollView, Alert} from 'react-native';
import React, {useState} from 'react';
import {Backround, SecondaryHint} from '../../../constants/Color';
import Header from '../../../components/Inventory/Header';
import AppDropDown from '../../../components/Inventory/AppDropDown';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import AppButton from '../../../components/Inventory/AppButton';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Vendor} from '../../../server/types';
import {applyDefaultsVendor} from '../../../Validator/Inventory/Barcode';
import {
  createData,
  GetItemsParamsNoFilterNoReturn,
} from '../../../utils/PublicHelper';
import {getInventoryPort} from '../../../server/InstanceTypes';
import {MaterialColors} from '../../../constants/MaterialColors';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

type NavProps = {
  navigation: NativeStackNavigationProp<any>;
};

const Vendors: React.FC<NavProps> = ({navigation}) => {
  const [vendorNumber, setVendorNumber] = useState<string>('');
  const [firstName, setFirstName] = useState<string>('');
  const [lastName, setLastName] = useState<string>('');
  const [company, setCompany] = useState<string>('');
  const [address1, setAddress1] = useState<string>('');
  const [address2, setAddress2] = useState<string>('');
  const [city, setCity] = useState<string>('');
  const [state, setState] = useState<string>('');
  const [zipCode, setZipCode] = useState<string>('');
  const [phone, setPhone] = useState<string>('');
  const [vendorTerms, setVendorTerms] = useState<string>('');

  const AddVendor = async () => {
    if (vendorNumber === '' || company === '' || firstName === '') {
      Alert.alert('Please Enter Required Field');
      return;
    }

    const getVendor = await GetItemsParamsNoFilterNoReturn(
      (await getInventoryPort()).toString(),
      '/getVendorDetails/:Vendor_Number',
      {Vendor_Number: vendorNumber},
    );

    if (Array.isArray(getVendor) && getVendor.length === 0) {
      const inventoryAdjustData: Partial<Vendor> = {
        Vendor_Number: vendorNumber,
        First_Name: firstName,
        Last_Name: lastName,
        Company: company,
        Address_1: address1,
        Address_2: address2,
        City: city,
        State: state,
        Zip_Code: zipCode,
        Phone: phone,
        Vendor_Terms: vendorTerms,
      };
      const applyDefault = applyDefaultsVendor(inventoryAdjustData);
      const result = await createData<Vendor>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefault,
        endpoint: '/createvendor',
      });

      if (result) {
        Alert.alert('Vendor Added Success!');
      }
    } else {
      Alert.alert('Vendor Already Exists');
    }
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  return (
    <View
      style={{
        backgroundColor: colors.background, // Changed from Backround
        width: '100%',
        height: '100%',
        paddingHorizontal: wp('2.5%'),
      }}>
      <Header NavName="Add Vendor" />

      <View
        style={{
          paddingHorizontal: 10,
          flex: 1,
          justifyContent: 'space-between',
          height: 200,
        }}>
        <View style={{height: hp('76%')}}>
          <ScrollView showsVerticalScrollIndicator={false}>
            <AppTextInput
              PlaceHolder="Enter Vendor Number"
              Title="Vendor Number"
              Value={vendorNumber}
              onChangeText={text => setVendorNumber(text)}
              isRequired={true}
            />

            <AppTextInput
              PlaceHolder="Enter First Name"
              Title="First Name"
              Value={firstName}
              onChangeText={text => setFirstName(text)}
              isRequired={true}
            />

            <AppTextInput
              PlaceHolder="Enter Last Name"
              Title="Last Name"
              Value={lastName}
              onChangeText={text => setLastName(text)}
            />

            <AppTextInput
              PlaceHolder="Enter Company"
              Title="Company"
              Value={company}
              onChangeText={text => setCompany(text)}
              isRequired={true}
            />

            <AppTextInput
              PlaceHolder="Enter Address 1"
              Title="Address 1"
              Value={address1}
              onChangeText={text => setAddress1(text)}
            />

            <AppTextInput
              PlaceHolder="Enter Address 2"
              Title="Address 2"
              Value={address2}
              onChangeText={text => setAddress2(text)}
            />
            <AppTextInput
              PlaceHolder="Enter City"
              Title="City"
              Value={city}
              onChangeText={text => setCity(text)}
            />

            <AppTextInput
              PlaceHolder="Enter State"
              Title="State"
              Value={state}
              onChangeText={text => setState(text)}
            />

            <AppTextInput
              PlaceHolder="Enter Zip Code"
              Title="Zip Code"
              Value={zipCode}
              onChangeText={text => setZipCode(text)}
            />

            <AppTextInput
              PlaceHolder="Enter Phone"
              Title="Phone"
              Value={phone}
              onChangeText={text => setPhone(text)}
            />

            <AppTextInput
              PlaceHolder="Enter Vendor Terms"
              Title="Vendor Terms"
              Value={vendorTerms}
              onChangeText={text => setVendorTerms(text)}
            />
          </ScrollView>
        </View>

        <View
          style={{
            position: 'absolute',
            bottom: 0,
            right: 0,
            left: 0,
            backgroundColor: colors.surface, // Changed from MaterialColors.surface
            paddingVertical: hp('1%'),
            paddingHorizontal: wp('2.5%'),
          }}>
          <AppButton Title="Add Vendor" OnPress={() => AddVendor()} />
        </View>
      </View>
    </View>
  );
};

export default Vendors;
