import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  FlatList,
  Modal,
  Alert,
  Button,
  Platform,
  PermissionsAndroid,
  StatusBar,
  Keyboard,
} from 'react-native';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  Backround,
  Primary,
  Secondary,
  SecondaryHint,
} from '../../../constants/Color';
import Header from '../../../components/Inventory/Header';
import Search from '../../../components/Inventory/Search';
import AppButton from '../../../components/Inventory/AppButton';
import PurchaseOrderItemCart from '../../../components/Inventory/PurchaseOrderItemCart';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {
  BrandOrSubCategory,
  Brands,
  Department,
  Inventory,
  Inventory_Filter,
  Inventory_Vendors,
  PurchaseOrder,
  PurchaseOrderItems,
  SubCategories,
  UpdatePurchaseOrder,
  UpdatePurchaseOrderItems,
  Vendor,
  VendorItem,
} from '../../../server/types';
import {
  AdjustInventory,
  createData,
  findDifference,
  GetAllItems,
  GetAllItemsWithFilter,
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
  GetItemsWithParams,
  handleSearch,
  onSearchChange_CommonVendor,
  showAlert,
  showAlertOK,
  updateData,
} from '../../../utils/PublicHelper';
import DataList from '../../../components/Inventory/AppList';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import CustomCheckbox from '../../../components/Inventory/CustomCheckbox';
import {
  applyDefaultsInventoryVendorsUpdate,
  applyDefaultsPurchaseOrder,
  applyDefaultsPurchaseOrderItem,
  applyDefaultsPurchaseOrderItemUpdate,
  applyDefaultsUpdatePurchaseOrder,
} from '../../../Validator/Inventory/Barcode';
import {createItem, deleteItem, fetchSingleItem} from '../../../server/service';
import AppSwitch from '../../../components/Inventory/AppSwitch';
import GeneratePDF from '../../../components/Inventory/GenaratePDF';
import {getInventoryPort, getLotteryPort} from '../../../server/InstanceTypes';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import AppFocus from '../../../components/Inventory/AppFocus';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../../styles/fonts';
import RNFS from 'react-native-fs';
import Share from 'react-native-share';
import RNHTMLtoPDF from 'react-native-html-to-pdf';
import DocumentPicker from 'react-native-document-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AntDesign from 'react-native-vector-icons/AntDesign';
import FontAwesome6 from 'react-native-vector-icons/FontAwesome6';
import AppFilter from '../../../components/Inventory/AppFilter';
import AppSearchWIthFilter from '../../../components/Inventory/AppSearchWIthFilter';
import AppDropDown from '../../../components/Inventory/AppDropDown';
import AppLoader from '../../../components/Inventory/AppLoader';
import {hasPermission} from '../../../utils/permissionHelper';
import {MaterialColors} from '../../../constants/MaterialColors';
import FAB from '../../../components/common/FAB';
import {Reorder_Status} from '../../../Types/Lottery/Lottery_Types';
import AppScanner from '../../../components/Inventory/AppScanner';
import {useCodeScanner} from 'react-native-vision-camera';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';
type CountItemRouteProp = RouteProp<any, 'PurchaseOrderItem'>;

const PurchaseOrderItem: React.FC<{
  route: CountItemRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [mainPO, setMainPO] = useState<PurchaseOrder>(route?.params?.ItemData);
  const [itemPO, setItemPO] = useState<PurchaseOrderItems[]>([]);
  const [initialPO, setInitialPO] = useState<PurchaseOrderItems[]>([]);
  const [itemPOFilter, setItemPOFilter] = useState<PurchaseOrderItems[]>([]);
  const [vendorItems, setVendorItems] = useState<VendorItem[]>([]);
  const [vendorItemsFilter, setVendorItemsFilter] = useState<VendorItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [supplierData, setSupplierData] = useState<VendorItem[]>([]);
  const [checkedState, setCheckedState] = useState({});
  const [selectedItems, setSelectedItems] = useState<VendorItem[]>([]);
  const [inputValues, setInputValues] = useState<
    {itemNum: string; value: string}[]
  >([]);
  const [isEnabled, setIsEnabled] = useState(false);
  const [closed, setClosed] = useState<string[]>([]);
  const [isClosed, setIsClosed] = useState<boolean>(false);

  const [totalCost, setTotalCost] = useState<number>(
    0 || route.params?.ItemData?.Total_Cost,
  );
  const [expectRecive, setExpectRecive] = useState<number>(
    0 || route.params?.ItemData?.ExpectedAmountToReceive,
  );
  const [success, setSuccess] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [searchQueryForModal, setSearchQueryForModal] = useState<string>('');
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [exist, setExists] = useState<Inventory>();

  const [isBarcodeScanned, setIsBarcodeScanned] = useState(true);
  const [barcode, setBarcode] = useState<string | null>('');
  const [selectedPath, setSelectedPath] = useState<string | null>(null);
  const textInputRef = useRef<TextInput>(null);
  const textInputRef2 = useRef<TextInput>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [showLookup, setshowLookup] = useState<boolean>(false);
  const [filter, setFilter] = useState<boolean>(false);
  const [isEnableFilter, setIsEnableFilter] = useState<boolean>(false);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [selectedVendor, setSelectedVendor] = useState<string>('');
  const [selectedBrand, setSelectedBrand] = useState<string>('');
  const [updateQty, setUpdateQty] = useState<string>('');
  const [selectedSubCategory, setSelectedSubCategory] = useState<string>('');

  const [brandorSubCat, SetBrandSubOrCat] = useState<BrandOrSubCategory[]>([]);

  const [getBrands, setGetBrands] = useState<Brands[]>([]);
  const [getSubCategories, setGetSubCategories] = useState<SubCategories[]>([]);
  const [inventoryData, setInventoryData] = useState<Inventory_Filter[]>([]);
  const [filteredData, setFilteredData] = useState<Inventory_Filter[]>([]);
  const [modal, setModal] = useState<boolean>(false);
  const [selectPoItems, setSelectPoItems] = useState<PurchaseOrderItems>();
  const [appLoader, setAppLoader] = useState<boolean>(false);
  const [isReorderd, setIsReorderd] = useState<boolean>(false);
  const [showLookupMain, setShowLookupMain] = useState<boolean>(false);
  const [camera, setCamera] = useState<boolean>(false);
  const [cameraModal, setCameraModal] = useState<boolean>(false);

  const handleSelectDirectory = async () => {
    try {
      const res = await DocumentPicker.pickDirectory();
      if (res && res.uri) {
        const mainPath = '/storage/emulated/0/';
        const extractPath = res.uri.replace(
          /^content:\/\/[^/]+\/tree\/primary%3A/,
          '',
        );
        const decodedPath = decodeURIComponent(extractPath);
        const finalPath = mainPath + encodeURIComponent(decodedPath);
        setSelectedPath(finalPath);
        console.log(finalPath, 'FINAL PATH');

        // Check if the path is valid and selected
        if (finalPath) {
          generatePDF(finalPath);
          return true;
        } else {
          Alert.alert('Path Not Selected!');
          return false;
        }
      } else {
        console.log('No folder selected.');
        return false; // Path not selected
      }
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
        console.log('User canceled the picker');
      } else {
        console.error('DocumentPicker Error: ', err);
        Alert.alert('Error', 'Failed to select a directory');
      }
      return false; // If an error occurs or user cancels, return false
    }
  };

  useEffect(() => {
    if (
      selectedBrand ||
      selectedDepartment ||
      selectedVendor ||
      selectedSubCategory
    ) {
      setIsEnableFilter(true);
    } else {
      setIsEnableFilter(false);
    }
  }, [selectedBrand, selectedDepartment, selectedVendor, selectedSubCategory]);

  useEffect(() => {
    const getFilteredData = vendorItems.filter(item => {
      const matchesDepartment = selectedDepartment
        ? item.Dept_ID === selectedDepartment
        : true;
      const matchesVendor = selectedVendor
        ? item.Vendor_Number === selectedVendor
        : true;
      const matchesBrand = selectedBrand ? item.Brand === selectedBrand : true;
      const matchesSubCategory = selectedSubCategory
        ? item.SubCategory === selectedSubCategory
        : true;
      return (
        matchesDepartment && matchesVendor && matchesBrand && matchesSubCategory
      );
    });
    setVendorItemsFilter(getFilteredData);
  }, [
    selectedDepartment,
    selectedVendor,
    selectedBrand,
    selectedSubCategory,
    vendorItems,
  ]);
  const requestWritePermissions = async () => {
    if (Platform.OS === 'android') {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
      );
      if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
        Alert.alert('Permission Denied', 'Storage permission is required');
        return false;
      }
    }
    return true;
  };

  // Generate PDF
  const generatePDF = async (PdfPath: string) => {
    // if (!PdfPath) {
    //   Alert.alert('No Directory Selected', 'Please select a directory first.');
    //   return;
    // }

    // Request storage permissions (for Android)
    const hasPermission = await requestWritePermissions();
    if (!hasPermission) return;

    // Dynamically create HTML content based on PurchaseOrder and PurchaseOrderItems data
    const generateOrderItemsHTML = () => {
      return itemPO
        .map(item => {
          return `
            <tr>
              <td>${item.ItemName}</td>
              <td>${item.Quan_Ordered}</td>
              <td>${item.CostPer}</td>
              <td>${(item.CasePack * item.NumberPerCase || 0).toFixed(2)}</td>
            </tr>
          `;
        })
        .join('');
    };

    // Create the HTML content for the PDF
    const htmlContent = `
        <html>
          <body style="font-family: Arial, sans-serif;">
            <h1 style="text-align: center;">Purchase Order</h1>
            <h3 style="text-align: center;">PO #${mainPO?.PO_Number}</h3>
            <p style="font-size: 16px; margin-top: 20px;">Supplier: ${
              mainPO?.Vendor_Number
            }</p>
            <p style="font-size: 16px;">Order Date: ${mainPO?.DateTime}</p>
  
            <h3 style="margin-top: 20px;">Order Details:</h3>
            <table border="1" style="width: 100%; border-collapse: collapse;">
              <thead>
                <tr>
                  <th style="padding: 8px; text-align: left;">Item</th>
                  <th style="padding: 8px; text-align: left;">Quantity</th>
                  <th style="padding: 8px; text-align: left;">Unit Price</th>
                  <th style="padding: 8px; text-align: left;">Total</th>
                </tr>
              </thead>
              <tbody>
                ${generateOrderItemsHTML()}
              </tbody>
            </table>
  
            <h3 style="margin-top: 20px;">Total Amount: $${mainPO?.Total_Cost.toFixed(
              2,
            )}</h3>
          </body>
        </html>
      `;

    // Convert HTML to PDF
    const options = {
      html: htmlContent,
    };

    try {
      const file = await RNHTMLtoPDF.convert(options);
      const filePath = file.filePath;
      const newFilePath = `${PdfPath}/${
        'Purchase_Order_' + mainPO?.PO_Number
      }.pdf`;

      // Save the generated PDF file to the selected directory
      await RNFS.moveFile(filePath, newFilePath);
      Alert.alert('Success', `PDF saved at ${newFilePath}`);

      // Share the PDF
      sharePDF(newFilePath);
    } catch (error) {
      console.error('Error generating or saving PDF:', error);
      Alert.alert('Error', 'Failed to generate PDF');
    }
  };

  // Share the PDFfr
  const sharePDF = async (filePath: string) => {
    try {
      const options = {
        title: 'Share Purchase Order PDF',
        url: `file://${filePath}`, // Use the correct file URL format for your platform
        type: 'application/pdf',
      };
      await Share.open(options);
    } catch (error) {
      console.error('Error sharing the PDF:', error);
      Alert.alert('Error', 'Failed to share PDF');
    }
  };
  useFocusEffect(
    useCallback(() => {
      setIsBarcodeScanned(false);
      getReorderStatus();

      setSearchQueryForModal('');
      setShowLookupMain(false);
      setshowLookup(false);
      setTimeout(() => {
        if (textInputRef2.current) {
          textInputRef2.current.blur();
          setTimeout(() => {
            if (textInputRef2.current) {
              textInputRef2.current.focus();
            }
          }, 50);
        }
      }, 50);
      setTimeout(() => {
        if (textInputRef.current) {
          textInputRef.current.blur();
          setTimeout(() => {
            if (textInputRef.current) {
              textInputRef.current.focus();
            }
          }, 50);
        }
      }, 50);
    }, []),
  );

  useFocusEffect(
    useCallback(() => {
      getIntialDetals();
    }, []),
  );

  const getReorderStatus = async () => {
    const getReorderStatus = await GetItemsParamsNoFilterNoReturn(
      (await getLotteryPort()).toString(),
      '/GetReorderStatus/:PO_Number',
      {PO_Number: mainPO.PO_Number},
    );
    if (Array.isArray(getReorderStatus) && getReorderStatus.length === 0) {
      setIsReorderd(true);
    } else {
      setIsReorderd(false);
    }
  };
  const onRefresh = async () => {
    setRefreshing(true);
    setPage(1);

    try {
      await getIntialDetals();
    } catch (error) {
      console.error('Error refreshing data:', error);
    }

    setRefreshing(false);
  };

  const getIntialDetals = async () => {
    GetItemsWithParams(
      (await getInventoryPort()).toString(),
      '/getpurchaseorderitems/:PO_Number',
      setItemPO,
      setItemPOFilter,
      setLoading,
      {PO_Number: route?.params?.ItemData?.PO_Number},
    );
    GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/getpurchaseorderitems/:PO_Number',
      setInitialPO,
      {PO_Number: route?.params?.ItemData?.PO_Number},
      false,
    );
    GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/getvendoritems/:Vendor_Number',
      setVendorItems,
      {
        Vendor_Number:
          route?.params?.ItemData?.Vendor_Number || mainPO.Vendor_Number,
      },
    );

    GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/getvendoritems/:Vendor_Number',
      setVendorItemsFilter,
      {
        Vendor_Number:
          route?.params?.ItemData?.Vendor_Number || mainPO.Vendor_Number,
      },
    );
    GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/getpounique/:PO_Number',
      setMainPO,
      {PO_Number: route?.params?.ItemData?.PO_Number || mainPO.PO_Number},
      true,
    );

    if (route?.params?.ItemData?.Status === 'O') {
      handleToggleSwitch({value: true, isInitial: true});
    }

    GetAllItemsWithFilter(
      (await getInventoryPort()).toString(),
      '/inventorynopg',
      setInventoryData,
      setFilteredData,
      setLoading,
      false,
    );
  };

  const getAllVendorItems = async () => {
    GetAllItemsWithFilter(
      (await getInventoryPort()).toString(),
      '/getvendoritemsAll',
      setVendorItems,
      setVendorItemsFilter,
      setLoading,
      false,
    );
  };

  const toggleLookup = useCallback((value: boolean) => {
    setVendorItemsFilter(vendorItems);
    setshowLookup(value);
    setSearchQueryForModal('');

    if (Platform.OS === 'android') {
      if (value) {
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.blur();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 50);
          }
        }, 50);
      } else {
        Keyboard.dismiss();
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.blur();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 50);
          }
        }, 50);
      }
      return;
    }

    // iOS handling
    if (value) {
      setTimeout(() => {
        textInputRef.current?.focus();
      }, 100);
    } else {
      setSearchQueryForModal('');
      Keyboard.dismiss();
    }
  }, []);

  const toggleLookupRefMain = useCallback(async (value: boolean) => {
    GetItemsWithParams(
      (await getInventoryPort()).toString(),
      '/getpurchaseorderitems/:PO_Number',
      setItemPO,
      setItemPOFilter,
      setLoading,
      {PO_Number: route?.params?.ItemData?.PO_Number},
    );
    setShowLookupMain(value);
    setSearchQuery('');
    if (Platform.OS === 'android') {
      if (value) {
        setSearchQuery('');
        setTimeout(() => {
          if (textInputRef2.current) {
            textInputRef2.current.blur();
            setTimeout(() => {
              if (textInputRef2.current) {
                textInputRef2.current.focus();
              }
            }, 50);
          }
        }, 50);
      } else {
        Keyboard.dismiss();
        setTimeout(() => {
          if (textInputRef2.current) {
            textInputRef2.current.blur();
            setTimeout(() => {
              if (textInputRef2.current) {
                textInputRef2.current.focus();
              }
            }, 50);
          }
        }, 50);
      }
      return;
    }

    // iOS handling
    if (value) {
      setTimeout(() => {
        textInputRef2.current?.focus();
      }, 100);
    } else {
      Keyboard.dismiss();
    }
  }, []);

  const defaultStatus = route?.params?.ItemData?.Status === 'O' ? 'O' : 'C';
  const defaultPoType =
    route?.params?.ItemData?.POType === 0
      ? 0
      : route?.params?.ItemData?.POType === 1
      ? 1
      : 2;

  useEffect(() => {
    if (itemPO.length > 0) {
      itemPO.forEach(item => {
        handleCheckboxChange(item, true);
      });
    }
  }, [itemPO]);

  useEffect(() => {
    if (success) {
      updatePO();
    }
  }, [totalCost, expectRecive]);

  const removeListItem = (
    PONumber: number,
    Barcode: string,
    PoItems: PurchaseOrderItems,
  ) => {
    showAlert('Are you sure you want to delete?')
      .then(async result => {
        if (result) {
          const calculatedQty =
            PoItems.Quan_Ordered > 1
              ? Number(PoItems.Quan_Ordered) * Number(PoItems.CostPer)
              : Number(PoItems.CostPer);
          const detleteCost = Number(mainPO.Total_Cost) - Number(calculatedQty);
          const detleteExpected =
            Number(mainPO.ExpectedAmountToReceive) -
            Number(PoItems.Quan_Ordered);

          if (itemPOFilter.length === 1) {
            const result = await deleteItem(
              (await getInventoryPort()).toString(),
              '/DeletePOItems/:PO_Number/:ItemNum',
              {PO_Number: PONumber, ItemNum: Barcode},
            );
            console.log(result, 'DELETE ReSUT');

            if (result.success) {
              const updatedArray = itemPO.filter(
                item => item.ItemNum !== Barcode,
              );
              setItemPO(updatedArray);
              setItemPOFilter(updatedArray);
              updatePO('O', 0.0, 0, true);
            }
          } else {
            const result = await deleteItem(
              (await getInventoryPort()).toString(),
              '/DeletePOItems/:PO_Number/:ItemNum',
              {PO_Number: PONumber, ItemNum: Barcode},
            );
            console.log(result, 'DELETE ReSUT');

            if (result.success) {
              const updatedArray = itemPO.filter(
                item => item.ItemNum !== Barcode,
              );
              setItemPO(updatedArray);
              setItemPOFilter(updatedArray);
              updatePO('O', detleteCost, detleteExpected, true);
            }
          }
        }
      })
      .catch(error => {
        console.error('Error showing alert', error);
      });
  };

  const handleCheckboxChange = (
    item: Inventory,
    isChecked: boolean,
    isCheckBox: boolean = false,
  ) => {
    if (isChecked) {
      // Update checked state
      setCheckedState(prevState => ({
        ...prevState,
        [String(item?.ItemNum)]: isChecked,
      }));
    } else {
      // Remove object by item.ItemNum when isCheckBox is true
      setCheckedState(prevState => {
        const newState = {...prevState};
        delete newState[String(item?.ItemNum)]; // Remove the key corresponding to ItemNum
        return newState;
      });
    }

    if (isCheckBox) {
      // Update selected items
      setSelectedItems((prevItems: Inventory[]) => {
        if (isChecked) {
          return [...prevItems, item];
        } else {
          return prevItems.filter(
            selected => selected?.ItemNum !== item?.ItemNum,
          );
        }
      });
    }
  };

  const updatePO = async (
    Status?: string,
    TotalCost?: number,
    Expected?: number,
    isDelete?: boolean,
  ) => {
    const poItmes: Partial<UpdatePurchaseOrder> = {
      PO_Number: mainPO.PO_Number,
      Total_Cost: !isDelete ? totalCost : TotalCost,
      Total_Cost_Received:
        itemPOFilter.length === 1 ? mainPO.Total_Cost_Received : TotalCost,
      Total_Charges: mainPO.Total_Charges,
      ExpectedAmountToReceive: !isDelete ? expectRecive : Expected,
      Store_ID: mainPO.Store_ID,
      DateTime: mainPO.DateTime,
      Reference: mainPO.Reference,
      Vendor_Number: mainPO.Vendor_Number,
      Ship_Via: mainPO.Ship_Via,
      Status: Status || defaultStatus,
      Cashier_ID: mainPO.Cashier_ID,
      Due_Date: mainPO.Due_Date,
      Last_Modified: mainPO.Last_Modified,
      Cancel_Date: mainPO.DateTime,
      Order_Reason: mainPO.Order_Reason,
      POType: defaultPoType,
    };

    const applyDefault = applyDefaultsUpdatePurchaseOrder(poItmes);

    const result = await updateData<UpdatePurchaseOrder>({
      baseURL: (await getInventoryPort()).toString(),
      data: applyDefault,
      endpoint: '/updatePoSummary',
    });
    if (result) {
      GetItemsWithParams(
        (await getInventoryPort()).toString(),
        '/getpurchaseorderitems/:PO_Number',
        setItemPO,
        setItemPOFilter,
        setLoading,
        {PO_Number: route?.params?.ItemData?.PO_Number},
      );
      GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/getpounique/:PO_Number',
        setMainPO,
        {
          PO_Number: route?.params?.ItemData?.PO_Number || mainPO.PO_Number,
        },
        true,
      );
    } else {
      console.log('Error!!');
    }
  };

  const updatePOForUpdateqty = async (
    Status?: string,
    TotalCost?: number,
    Expected?: number,
    isDelete?: boolean,
  ) => {
    const poItmes: Partial<UpdatePurchaseOrder> = {
      PO_Number: mainPO.PO_Number,
      Total_Cost: !isDelete ? totalCost : TotalCost,
      Total_Cost_Received:
        Number(mainPO.Total_Cost_Received) > 0
          ? Number(mainPO.Total_Cost_Received)
          : 0,
      Total_Charges: mainPO.Total_Charges,
      ExpectedAmountToReceive: !isDelete ? expectRecive : Expected,
      Store_ID: mainPO.Store_ID,
      DateTime: mainPO.DateTime,
      Reference: mainPO.Reference,
      Vendor_Number: mainPO.Vendor_Number,
      Ship_Via: mainPO.Ship_Via,
      Status: Status || defaultStatus,
      Cashier_ID: mainPO.Cashier_ID,
      Due_Date: mainPO.Due_Date,
      Last_Modified: mainPO.Last_Modified,
      Cancel_Date: mainPO.DateTime,
      Order_Reason: mainPO.Order_Reason,
      POType: defaultPoType,
    };

    const applyDefault = applyDefaultsUpdatePurchaseOrder(poItmes);

    const result = await updateData<UpdatePurchaseOrder>({
      baseURL: (await getInventoryPort()).toString(),
      data: applyDefault,
      endpoint: '/updatePoSummary',
    });
    if (result) {
      GetItemsWithParams(
        (await getInventoryPort()).toString(),
        '/getpurchaseorderitems/:PO_Number',
        setItemPO,
        setItemPOFilter,
        setLoading,
        {PO_Number: route?.params?.ItemData?.PO_Number},
      );
      GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/getpounique/:PO_Number',
        setMainPO,
        {
          PO_Number: route?.params?.ItemData?.PO_Number || mainPO.PO_Number,
        },
        true,
      );
    } else {
      console.log('Error!!');
    }
  };

  const updatePOForReciveALL = async (Status?: string, TotalCost?: number) => {
    const poItmes: Partial<UpdatePurchaseOrder> = {
      PO_Number: mainPO.PO_Number,
      Total_Cost: mainPO.Total_Cost,
      Total_Cost_Received: TotalCost,
      Total_Charges: mainPO.Total_Charges,
      ExpectedAmountToReceive: mainPO.ExpectedAmountToReceive,
      Store_ID: mainPO.Store_ID,
      DateTime: mainPO.DateTime,
      Reference: mainPO.Reference,
      Vendor_Number: mainPO.Vendor_Number,
      Ship_Via: mainPO.Ship_Via,
      Status: Status || defaultStatus,
      Cashier_ID: mainPO.Cashier_ID,
      Due_Date: mainPO.Due_Date,
      Last_Modified: mainPO.Last_Modified,
      Cancel_Date: mainPO.DateTime,
      Order_Reason: mainPO.Order_Reason,
      POType: defaultPoType,
    };

    const applyDefault = applyDefaultsUpdatePurchaseOrder(poItmes);

    const result = await updateData<UpdatePurchaseOrder>({
      baseURL: (await getInventoryPort()).toString(),
      data: applyDefault,
      endpoint: '/updatePoSummary',
    });
    if (result) {
      GetItemsWithParams(
        (await getInventoryPort()).toString(),
        '/getpurchaseorderitems/:PO_Number',
        setItemPO,
        setItemPOFilter,
        setLoading,
        {PO_Number: route?.params?.ItemData?.PO_Number},
      );
      GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/getpounique/:PO_Number',
        setMainPO,
        {
          PO_Number: route?.params?.ItemData?.PO_Number || mainPO.PO_Number,
        },
        true,
      );
    } else {
      console.log('Error!!');
    }
  };

  const handleToggleSwitch = async ({
    value,
    isInitial = false,
  }: {
    value: boolean;
    isInitial: boolean;
  }) => {
    try {
      if (isInitial) {
        setIsEnabled(value);
      } else {
        console.log('ITEMMMM', itemPOFilter);
        const problematicItems = itemPOFilter.filter(item => {
          return (
            Number(item.Quan_Received) === 0 && Number(item.Quan_Damaged) === 0
          );
        });
        const alertMessage =
          problematicItems.length > 0
            ? 'Closing this purchase order without updating received quantities may cause inventory inconsistencies. Are you sure you want to close this order?'
            : 'Are you sure you want to close this purchase order?';
        await showAlert(alertMessage)
          .then(async result => {
            if (result) {
              setIsClosed(true);
              const poItmes: Partial<UpdatePurchaseOrder> = {
                PO_Number: mainPO.PO_Number,
                Total_Cost: mainPO.Total_Cost,
                Total_Cost_Received: mainPO.Total_Cost_Received,
                Total_Charges: mainPO.Total_Charges,
                ExpectedAmountToReceive: mainPO.ExpectedAmountToReceive,
                Store_ID: mainPO.Store_ID,
                DateTime: mainPO.DateTime,
                Reference: mainPO.Reference,
                Vendor_Number: mainPO.Vendor_Number,
                Ship_Via: mainPO.Ship_Via,
                Status: 'C',
                Cashier_ID: mainPO.Cashier_ID,
                Due_Date: mainPO.Due_Date,
                Last_Modified: mainPO.Last_Modified,
                Cancel_Date: mainPO.DateTime,
                Order_Reason: mainPO.Order_Reason,
                POType: mainPO.POType,
                Dirty: mainPO.Dirty,
                Print_Notes_On_PO: mainPO.Print_Notes_On_PO,
                ShipTo_1: mainPO.ShipTo_1,
                ShipTo_2: mainPO.ShipTo_2,
                ShipTo_4: mainPO.ShipTo_4,
                Terms: mainPO.Terms,
              };

              const applyDefault = applyDefaultsUpdatePurchaseOrder(poItmes);
              console.log(applyDefault);

              const result = await updateData<UpdatePurchaseOrder>({
                baseURL: (await getInventoryPort()).toString(),
                data: applyDefault,
                endpoint: '/updatePoSummary',
              });
              console.log(result);

              if (result) {
                setIsEnabled(value);
                setIsClosed(true);
                Alert.alert('PO Closed!');
              }
            }
          })
          .catch(error => {
            console.error('Error showing alert', error);
          });
      }
    } catch (error) {
      console.log(error);
    }
  };

  const renderModalItem = ({item}: {item: VendorItem}) => {
    const inputValue =
      inputValues.find(input => input.itemNum === item.ItemNum)?.value ||
      itemPO.find(input => input.ItemNum === item.ItemNum)?.CasePack ||
      itemPO.find(input => input.ItemNum === item.ItemNum)?.Quan_Ordered ||
      '';
    const existVal =
      inputValue === null || inputValue === undefined ? false : true;

    return (
      <TouchableOpacity
        style={styles.vendorItemCard}
        onPress={() => VendorItemAdd(item)}>
        <View style={styles.vendorItemInfo}>
          <Text style={styles.vendorItemName}>{item.ItemName}</Text>
          <Text style={styles.vendorItemDetail}>
            Per Case: {item.NumPerVenCase}
          </Text>
        </View>

        <Text style={styles.vendorItemPrice}>${item.CostPer.toFixed(2)}</Text>
      </TouchableOpacity>
    );
  };

  const isItemNumExist = (itemNum: string) => {
    return itemPO.some(item => item.ItemNum === itemNum);
  };

  const VendorItemAdd = (item: VendorItem) => {
    const IsExists = isItemNumExist(item.ItemNum);
    if (IsExists) {
      Alert.alert('Item Already Exists');
    } else {
      setModalVisible(false);
      navigation.navigate('PurchaseOrderQuanity', {
        ItemData: item,
        Main: mainPO,
      });
    }
  };

  const renderItem = ({item}: {item: PurchaseOrderItems}) => {
    return (
      <PurchaseOrderItemCart
        Name={item?.ItemName}
        Ordered={item?.Quan_Ordered}
        RecivedNow={item?.Quan_Received}
        DamagedNow={item.Quan_Damaged}
        POType={true}
        OnPress={async () => {
          const isAuthorized = await hasPermission('CFA_HH_PO_Receive');

          if (!isAuthorized) {
            Alert.alert('You do not have permission to view receive item.');
            return;
          }

          const getMainPOLatest = await GetItemsParamsNoFilterNoReturn(
            (await getInventoryPort()).toString(),
            '/getpounique/:PO_Number',
            {
              PO_Number: route?.params?.ItemData?.PO_Number || mainPO.PO_Number,
            },
          );

          navigation.navigate('PurchaseItemView', {
            ItemData: item,
            MainPO: getMainPOLatest[0],
          });
        }}
        Delete={() => removeListItem(item?.PO_Number, item.ItemNum, item)}
        Edit={() => {
          setModal(true);
          setSelectPoItems(item);
        }}
        ItemCost={Number(item.Quan_Ordered) * Number(item.CostPer)}
      />
    );
  };

  const onSearchChange = async (text: string) => {
    setCamera(false);
    const getBarcode = await GetItemsParamsNoFilterNoReturn(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      {ItemNum: text},
    );
    setSearchQuery(getBarcode[0]?.ItemNum || text);
    if (text) {
      if (showLookupMain) {
        handleSearch(
          text,
          itemPO,
          ['ItemName', 'ItemNum'],
          setItemPOFilter,
          setLoading,
        );
      } else {
        if (Array.isArray(getBarcode) && getBarcode.length === 0) {
          const userConfirmed = await showAlert(
            'Item not found. Would you like to add it as a new item?',
          );
          if (userConfirmed) {
            navigation.navigate('ItemType', {
              ItemData: getBarcode[0]?.ItemNum || text,
            });
          } else {
            if (textInputRef2.current) {
              textInputRef2.current.clear();
              textInputRef2.current.blur();
            }

            setSearchQuery('');
            setShowLookupMain(false);
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef2.current) {
                textInputRef2.current.focus();
              }
            }, 200);
          }
        } else {
          const FiterExist = isItemNumExist(
            Array.isArray(getBarcode) && getBarcode.length === 0
              ? text
              : getBarcode[0]?.ItemNum,
          );

          if (FiterExist) {
            handleSearch(
              getBarcode[0]?.ItemNum || text,
              itemPO,
              ['ItemName', 'ItemNum'],
              setItemPOFilter,
              setLoading,
            );
          } else {
            const GetVendorItem = vendorItems.filter(
              item =>
                item.ItemNum === getBarcode[0]?.ItemNum || getBarcode.ItemNum,
            );

            console.log('GetVendorItem', GetVendorItem);
            if (GetVendorItem.length > 0) {
              setModalVisible(false);
              navigation.navigate('PurchaseOrderQuanity', {
                ItemData: GetVendorItem[0],
                Main: mainPO,
              });
            } else {
              showAlert(
                'Item not currently linked to this vendor. Link item to vendor?',
              )
                .then(async result => {
                  if (result) {
                    setModalVisible(false);
                    const AssingVendor = await GetItemsParamsNoFilterNoReturn<
                      VendorItem[]
                    >(
                      (await getInventoryPort()).toString(),
                      '/getvendoritemsByItems/:ItemNum',
                      {ItemNum: getBarcode[0]?.ItemNum || getBarcode.ItemNum},
                    );

                    if (AssingVendor) {
                      const storeId = await AsyncStorage.getItem('STOREID');
                      const ValidStore = storeId === null ? '1001' : storeId;
                      const Inventory_Vendor: Partial<Inventory_Vendors> = {
                        Store_ID: ValidStore,
                        ItemNum: getBarcode[0]?.ItemNum || getBarcode.ItemNum,
                        Vendor_Number: mainPO.Vendor_Number,
                      };

                      const inventoryWithDefaults =
                        applyDefaultsInventoryVendorsUpdate(Inventory_Vendor);

                      const result = await createData<Inventory>({
                        baseURL: (await getInventoryPort()).toString(),
                        data: inventoryWithDefaults,
                        endpoint: '/createinvedors',
                      });

                      if (result) {
                        navigation.navigate('PurchaseOrderQuanity', {
                          ItemData: AssingVendor[0],
                          Main: mainPO,
                        });
                      }
                    }
                  } else {
                    if (textInputRef2.current) {
                      textInputRef2.current.clear();
                      textInputRef2.current.blur();
                    }

                    setSearchQuery('');
                    setShowLookupMain(false);
                    Keyboard.dismiss();
                    setTimeout(() => {
                      if (textInputRef2.current) {
                        textInputRef2.current.focus();
                      }
                    }, 200);
                  }
                })
                .catch(error => {
                  console.error('Error showing alert', error);
                });
            }
          }
        }
      }
    } else {
      setItemPOFilter(itemPO);
    }
  };

  const InputReorderStockLevel = async () => {
    const results = await findDifference(vendorItemsFilter, itemPOFilter);

    const InventoryFilter = filteredData.filter(
      item =>
        item.In_Stock <= item?.Reorder_Level &&
        item.Vendor_Number === mainPO.Vendor_Number,
    );

    showAlert(
      `${InventoryFilter.length} Item has Been Found Would You Like to Proceed?`,
    )
      .then(async result => {
        if (result) {
          let totalCost = 0;
          let expectedReceive = 0;

          for (const data of InventoryFilter) {
            setAppLoader(true);
            const exists = itemPOFilter.some(
              item => item.ItemNum === data.ItemNum,
            );
            if (!exists) {
              // Accumulate totals
              const checkCaseQty =
                data.NumPerCase <= 0
                  ? Number(data.Reorder_Quantity)
                  : Number(data.NumPerCase) * Number(data.Reorder_Quantity);

              const itemTotalCost =
                Number(checkCaseQty) > 1
                  ? Number(checkCaseQty) * Number(data.Cost)
                  : data.Cost;

              const itemExpectedReceive = Number(
                data.NumPerCase === 0 ? data.Reorder_Quantity : checkCaseQty,
              );

              totalCost += itemTotalCost;
              expectedReceive += itemExpectedReceive;

              await AddItems(data);
            }
          }
          const RoStatus: Reorder_Status = {
            PO_Number: mainPO.PO_Number,
            IsReorderd: true,
          };

          const result = await createItem(
            (await getLotteryPort()).toString(),
            '/createreorderstatus',
            RoStatus,
          );
          if (result) {
            getIntialDetals();
            setAppLoader(false);
            setIsReorderd(false);
          }
        }
      })
      .catch(error => {
        console.error('Error showing alert', error);
      });
  };

  const AddItems = async (ReOrderItems: Inventory) => {
    const storeId = await AsyncStorage.getItem('STOREID');
    const ValidStore = storeId === null ? '1001' : storeId;

    try {
      const checkCaseQty =
        ReOrderItems.NumPerCase === 0
          ? Number(ReOrderItems.Reorder_Quantity)
          : Number(ReOrderItems.NumPerCase) *
            Number(ReOrderItems.Reorder_Quantity);
      const checkNumCase =
        ReOrderItems.NumPerCase === 0 ? 0 : ReOrderItems.NumPerCase;

      const TotalCost =
        Number(checkCaseQty) > 1
          ? Number(checkCaseQty) * Number(ReOrderItems.Cost)
          : 0;

      const ExpectedRecive = Number(
        ReOrderItems.NumPerCase === 0
          ? ReOrderItems.Reorder_Quantity
          : checkCaseQty,
      );

      const cashPack =
        ReOrderItems.NumPerCase === 0
          ? 0
          : Number(ReOrderItems.Reorder_Quantity);

      const poItmes: Partial<PurchaseOrderItems> = {
        PO_Number: mainPO.PO_Number,
        ItemNum: ReOrderItems.ItemNum,
        Quan_Ordered: checkCaseQty,
        CostPer: ReOrderItems.Cost,
        Quan_Received: 0,
        Vendor_Part_Number: ReOrderItems.Vendor_Part_Num,
        CasePack: cashPack,
        Store_ID: ValidStore,
        destStore_ID: ValidStore,
        NumberPerCase: checkNumCase,
      };

      const applyDefault = applyDefaultsPurchaseOrderItem(poItmes);
      const createResult = await createData<PurchaseOrderItems>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefault,
        endpoint: '/createpodetails',
      });

      if (createResult) {
        const RefreshedPO = await GetItemsParamsNoFilterNoReturn(
          (await getInventoryPort()).toString(),
          '/getpounique/:PO_Number',
          {PO_Number: route?.params?.ItemData?.PO_Number || mainPO.PO_Number},
        );

        const calExistTotal =
          Number(RefreshedPO[0].Total_Cost) + Number(TotalCost);
        const calExistExpected =
          Number(RefreshedPO[0].ExpectedAmountToReceive) +
          Number(ExpectedRecive);

        await updatePOReOrder(calExistTotal, calExistExpected);
      } else {
        console.log('Error');
      }
    } catch (error) {
      console.log(error);
    }
  };

  const updatePOReOrder = async (TotalCost?: number, Expected?: number) => {
    const poItmes: Partial<UpdatePurchaseOrder> = {
      PO_Number: mainPO.PO_Number,
      Total_Cost: TotalCost,
      Total_Cost_Received: 0,
      Total_Charges: 0,
      ExpectedAmountToReceive: Expected,
      Store_ID: mainPO.Store_ID,
      DateTime: mainPO.DateTime,
      Reference: mainPO.Reference,
      Vendor_Number: mainPO.Vendor_Number,
      Ship_Via: mainPO.Ship_Via,
      Status: 'O',
      Cashier_ID: mainPO.Cashier_ID,
      Due_Date: mainPO.Due_Date,
      Last_Modified: mainPO.Last_Modified,
      Cancel_Date: mainPO.DateTime,
      Order_Reason: mainPO.Order_Reason,
      POType: 0,
      Dirty: true,
      Print_Notes_On_PO: true,
      ShipTo_1: mainPO.ShipTo_1,
      ShipTo_2: mainPO.ShipTo_2,
      ShipTo_4: mainPO.ShipTo_4,
      Terms: '',
    };

    const applyDefault = applyDefaultsUpdatePurchaseOrder(poItmes);

    const result = await updateData<UpdatePurchaseOrder>({
      baseURL: (await getInventoryPort()).toString(),
      data: applyDefault,
      endpoint: '/updatePoSummary',
    });
  };

  const UpdateLatestQty = async () => {
    if (!updateQty || updateQty === '') {
      showAlertOK(
        `Please enter the amount you'd like to order`,
        'Quantity required',
      );
    } else {
      const poItmes: Partial<UpdatePurchaseOrderItems> = {
        PO_Number: mainPO.PO_Number,
        ItemNum: selectPoItems?.ItemNum,
        Quan_Ordered: Number(updateQty),
        Quan_Damaged: selectPoItems?.Quan_Damaged,
        CostPer: selectPoItems?.CostPer,
        Quan_Received: selectPoItems?.Quan_Received,
        Vendor_Part_Number: selectPoItems?.Vendor_Part_Number,
        CasePack: selectPoItems?.CasePack,
        Store_ID: selectPoItems?.Store_ID,
        destStore_ID: selectPoItems?.destStore_ID,
        NumberPerCase: selectPoItems?.NumberPerCase,
      };

      const applyDefault = applyDefaultsPurchaseOrderItemUpdate(poItmes);

      const result = await updateData<Inventory>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefault,
        endpoint: '/updatepodetails',
      });

      if (result) {
        if (Number(updateQty) > Number(selectPoItems?.Quan_Ordered)) {
          const calOldqunity =
            Number(updateQty) - Number(selectPoItems?.Quan_Ordered);
          const updateqtyTotal =
            Number(calOldqunity) * Number(selectPoItems?.CostPer);
          const TotalCal = Number(mainPO.Total_Cost) + Number(updateqtyTotal);
          const ExpectedCal =
            Number(mainPO.ExpectedAmountToReceive) + Number(calOldqunity);
          updatePOForUpdateqty('O', TotalCal, ExpectedCal, true);
          setModal(false);
          Alert.alert('Purchase Order Quanity Updated');
        } else {
          const updateqtyTotal =
            Number(updateQty) * Number(selectPoItems?.CostPer); // 100 * 0.7 = 70
          const TotalCaldec =
            Number(mainPO.Total_Cost) - Number(updateqtyTotal); // 439.20 - 70
          const ExpectedCaldec =
            Number(mainPO.ExpectedAmountToReceive) - // 200 - 70
            Number(updateQty);
          updatePOForUpdateqty('O', TotalCaldec, ExpectedCaldec, true);
          setModal(false);
          Alert.alert('Purchase Order Quanity Updated');
        }
      }
    }
  };

  const onChangeAddItems = async (text: string) => {
    setCameraModal(false);
    const getBarcode = await GetItemsParamsNoFilterNoReturn<Inventory_Filter>(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      {ItemNum: text},
    );
    setSearchQueryForModal(getBarcode[0]?.ItemNum || text);

    if (showLookup) {
      handleSearch(
        text,
        vendorItems,
        ['ItemName', 'ItemNum'],
        setVendorItemsFilter,
        setLoading,
      );
    } else {
      if (Array.isArray(getBarcode) && getBarcode.length === 0) {
        const userConfirmed = await showAlert(
          'Item not found. Do you want to create a new item?',
        );
        if (userConfirmed) {
          navigation.navigate('ItemType', {
            ItemData: getBarcode[0]?.ItemNum || text,
          });
        } else {
          setVendorItemsFilter(vendorItems);
          if (textInputRef.current) {
            textInputRef.current.clear();
            textInputRef.current.blur();
          }

          setSearchQuery('');
          setshowLookup(false);
          Keyboard.dismiss();
          setTimeout(() => {
            if (textInputRef.current) {
              textInputRef.current.focus();
            }
          }, 200);
        }
      } else {
        const FiterExist = isItemNumExist(
          Array.isArray(getBarcode) && getBarcode.length === 0
            ? text
            : getBarcode[0]?.ItemNum,
        );
        if (FiterExist) {
          showAlertOK(
            `This Item is Already Exists. In Current Purchase Order`,
            'Already Exists',
            'OK',
            () => {
              if (textInputRef.current) {
                textInputRef.current.clear();
                textInputRef.current.blur();
              }

              setSearchQuery('');
              setshowLookup(false);
              setSearchQueryForModal('');
              Keyboard.dismiss();
              setTimeout(() => {
                if (textInputRef.current) {
                  textInputRef.current.focus();
                }
              }, 200);
            },
          );
        } else {
          const GetVendorItem = vendorItems.filter(
            item =>
              item.ItemNum === getBarcode[0]?.ItemNum || getBarcode.ItemNum,
          );
          if (GetVendorItem.length > 0) {
            setModalVisible(false);
            navigation.navigate('PurchaseOrderQuanity', {
              ItemData: GetVendorItem[0],
              Main: mainPO,
            });
          } else {
            showAlert(
              'Item not currently linked to this vendor. Link item to vendor?',
            )
              .then(async result => {
                if (result) {
                  setModalVisible(false);
                  const AssingVendor = await GetItemsParamsNoFilterNoReturn<
                    VendorItem[]
                  >(
                    (await getInventoryPort()).toString(),
                    '/getvendoritemsByItems/:ItemNum',
                    {ItemNum: getBarcode[0]?.ItemNum || getBarcode.ItemNum},
                  );

                  if (AssingVendor) {
                    const storeId = await AsyncStorage.getItem('STOREID');
                    const ValidStore = storeId === null ? '1001' : storeId;
                    const Inventory_Vendor: Partial<Inventory_Vendors> = {
                      Store_ID: ValidStore,
                      ItemNum: getBarcode[0]?.ItemNum || text,
                      Vendor_Number: mainPO.Vendor_Number,
                    };

                    const inventoryWithDefaults =
                      applyDefaultsInventoryVendorsUpdate(Inventory_Vendor);

                    const result = await createData<Inventory>({
                      baseURL: (await getInventoryPort()).toString(),
                      data: inventoryWithDefaults,
                      endpoint: '/createinvedors',
                    });

                    if (result) {
                      navigation.navigate('PurchaseOrderQuanity', {
                        ItemData: AssingVendor[0],
                        Main: mainPO,
                      });
                    }
                  }
                } else {
                  if (textInputRef.current) {
                    textInputRef.current.clear();
                    textInputRef.current.blur();
                  }

                  setSearchQuery('');
                  setshowLookup(false);
                  setSearchQueryForModal('');
                  Keyboard.dismiss();
                  setTimeout(() => {
                    if (textInputRef.current) {
                      textInputRef.current.focus();
                    }
                  }, 200);
                }
              })
              .catch(error => {
                console.error('Error showing alert', error);
              });
          }
        }
      }
    }
  };
  const handleDoneClickMain = async () => {
    GetItemsWithParams(
      (await getInventoryPort()).toString(),
      '/getpurchaseorderitems/:PO_Number',
      setItemPO,
      setItemPOFilter,
      setLoading,
      {PO_Number: route?.params?.ItemData?.PO_Number},
    );
    if (textInputRef2.current) {
      textInputRef2.current.clear();
      textInputRef2.current.blur();
    }

    setSearchQuery('');
    setShowLookupMain(false);
    Keyboard.dismiss();
    setTimeout(() => {
      if (textInputRef2.current) {
        textInputRef2.current.focus();
      }
    }, 200);
  };
  const handleDoneClickSub = () => {
    setVendorItemsFilter(vendorItems);

    if (textInputRef.current) {
      textInputRef.current.clear();
      textInputRef.current.blur();
    }

    setSearchQuery('');
    setshowLookup(false);
    setSearchQueryForModal('');
    Keyboard.dismiss();
    setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.focus();
      }
    }, 200);
  };

  const codeScannerMain = useCodeScanner({
    codeTypes: [
      'qr',
      'ean-13',
      'upc-a',
      'ean-8',
      'upc-e',
      'code-128',
      'code-39',
      'code-93',
    ],
    onCodeScanned: codes => {
      if (codes.length > 0) {
        onSearchChange(codes[0].value);
      } else {
        console.log('No valid barcode detected.');
      }
    },
  });

  const codeScannerModal = useCodeScanner({
    codeTypes: [
      'qr',
      'ean-13',
      'upc-a',
      'ean-8',
      'upc-e',
      'code-128',
      'code-39',
      'code-93',
    ],
    onCodeScanned: codes => {
      if (codes.length > 0) {
        onChangeAddItems(codes[0].value);
      } else {
        console.log('No valid barcode detected.');
      }
    },
  });

  const ReceiveAllItmes = async () => {
    const problematicItems = itemPOFilter.filter(item => {
      return Number(item.Quan_Received) === 0;
    });

    if (problematicItems.length > 0) {
      const alertMessage = 'Are you sure you want to receive all items?';
      await showAlert(alertMessage)
        .then(async result => {
          if (result) {
            setAppLoader(true);
            for (const item of problematicItems) {
              if (Number(item.Quan_Received) === 0) {
                const poItmes: Partial<UpdatePurchaseOrderItems> = {
                  PO_Number: mainPO.PO_Number,
                  ItemNum: item.ItemNum,
                  Quan_Ordered: item.Quan_Ordered,
                  CostPer: item.CostPer,
                  Quan_Received: item.Quan_Ordered,
                  Vendor_Part_Number: item.Vendor_Part_Number,
                  CasePack: item.CasePack,
                  Store_ID: item.Store_ID,
                  destStore_ID: item.destStore_ID,
                  NumberPerCase: item.NumberPerCase,
                };

                const applyDefault =
                  applyDefaultsPurchaseOrderItemUpdate(poItmes);

                const result = await updateData<Inventory>({
                  baseURL: (await getInventoryPort()).toString(),
                  data: applyDefault,
                  endpoint: '/updatepodetails',
                });

                if (result) {
                  const TotalCost =
                    Number(item.Quan_Ordered) * Number(item.CostPer);

                  const getLatestPurchasedetails =
                    await GetItemsParamsNoFilterNoReturn(
                      (await getInventoryPort()).toString(),
                      '/getpounique/:PO_Number',
                      {
                        PO_Number:
                          route?.params?.ItemData?.PO_Number ||
                          mainPO.PO_Number,
                      },
                    );

                  await updatePOForReciveALL(
                    'O',
                    Number(getLatestPurchasedetails[0].Total_Cost_Received) +
                      Number(TotalCost),
                  );

                  await AdjustInventory(item.ItemNum, item.Quan_Ordered);
                }
              }
            }
            setAppLoader(false);
          } else {
            handleDoneClickMain();
          }
        })
        .catch(error => {
          console.error('Error showing alert', error);
        });
    } else {
      handleDoneClickMain();
    }
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      paddingHorizontal: wp('2.5%'),
    },
    modalContainer: {
      backgroundColor: 'rgba(0,0,0,0.5)',
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalOverlay: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    tableCell: {
      textAlign: 'left',
      fontSize: 16,
      paddingHorizontal: 5,
      color: colors.text,
    },
    itemNum: {
      width: 150,
    },
    itemName: {
      flex: 1,
    },
    price: {
      width: 80,
      textAlign: 'right',
    },
    searchInput: {
      height: 40,
      borderColor: colors.border,
      borderWidth: 1,
      borderRadius: 5,
      paddingLeft: 10,
      marginBottom: 10,
      marginHorizontal: 10,
      backgroundColor: colors.card,
      color: colors.text,
    },
    vendorItemCard: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('1.5%'),
      borderBottomColor: colors.border,
      borderBottomWidth: 1,
      backgroundColor: colors.card,
    },
    vendorItemInfo: {
      gap: 7,
    },
    vendorItemName: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: colors.text,
    },
    vendorItemDetail: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: colors.textSecondary,
    },
    vendorItemPrice: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.primary,
    },
  });

  return (
    <View
      style={{
        backgroundColor: colors.background, // Changed from Backround
        justifyContent: 'space-between',
      }}>
      <View style={{paddingHorizontal: wp('2.5%')}}>
        <Header
          NavName={'Purchase Order#: ' + mainPO.PO_Number}
          isProvid={true}
          Onpress={() =>
            navigation.navigate('PurchaseOrder', {
              ItemData: route?.params?.ItemData?.POType,
            })
          }
          isOption={true}
          Options={() => {
            if (textInputRef2.current) {
              textInputRef2.current.clear();
              textInputRef2.current.blur();
            }

            setSearchQuery('');
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef2.current) {
                textInputRef2.current.focus();
              }
            }, 200);
            setItemPOFilter(itemPO);
            setCamera(!camera);
          }}
        />
        <View>
          <View
            style={{
              backgroundColor: colors.surface, // Changed from Secondary
              paddingVertical: hp('1.5%'),
              borderRadius: 10,
              paddingHorizontal: wp('2.5%'),
              gap: 5,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <View>
              <Text
                style={{
                  fontFamily: Fonts.OnestBold,
                  fontSize: FontSizes.small,
                  color: colors.text, // Added theme color
                }}>
                Vendor: {mainPO.Company}
              </Text>
              <Text
                style={{
                  fontFamily: Fonts.OnestBold,
                  fontSize: FontSizes.small,
                  color: colors.textSecondary, // Changed from '#A1A1A1'
                }}>
                Refrence Number: {mainPO.Vendor_Number}
              </Text>
            </View>
            <View>
              <AppSwitch
                name="Status"
                value={isEnabled}
                onValueChange={handleToggleSwitch}
              />
            </View>
          </View>

          <View
            style={{
              flexDirection: 'column',
              alignItems: 'center',
              width: '100%',
              marginVertical: 2,
            }}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                columnGap: 10,
                alignItems: 'center',
                // columnGap: 30,
                width: '100%',
              }}>
              <TouchableOpacity
                style={{
                  paddingVertical: 10,
                  paddingHorizontal: 10,
                  backgroundColor: colors.primary, // Changed from Primary
                  flexDirection: 'row',
                  alignItems: 'center',
                  borderRadius: 10,
                }}
                onPress={() =>
                  // navigation.navigate('Barcode', {
                  //   VENDOR: mainPO.Vendor_Number,
                  //   MAINPO: mainPO,
                  //   ISPO: true,
                  // })
                  console.log('searcheuwe', searchQuery)
                }>
                <Text
                  style={{
                    fontFamily: Fonts.OnestBold,
                    fontSize: hp('1.8%'),
                    color: colors.background, // Changed from Backround
                  }}>
                  Add New Item
                </Text>
              </TouchableOpacity>
              {isReorderd && (
                <TouchableOpacity
                  style={{
                    paddingVertical: 10,
                    paddingHorizontal: 10,
                    backgroundColor: colors.primary, // Changed from Primary
                    flexDirection: 'row',
                    alignItems: 'center',
                    borderRadius: 10,
                    marginVertical: 5,
                  }}
                  onPress={() => InputReorderStockLevel()}>
                  <Text
                    style={{
                      fontFamily: Fonts.OnestBold,
                      fontSize: hp('1.8%'),
                      color: colors.background, // Changed from Backround
                    }}>
                    Restock Low Inventory
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>

          <AppSearchWIthFilter
            OnSearch={onSearchChange}
            SearchValue={searchQuery}
            Keyboardon={showLookupMain}
            textInputRef={textInputRef2}
            onToggleLookup={toggleLookupRefMain}
            OnSubmitEditing={handleDoneClickMain}
          />
        </View>
      </View>

      <View
        style={{
          paddingHorizontal: wp('2.5%'),
          height: hp('60%'),
          paddingBottom: hp('12%'),
        }}>
        <DataList
          data={itemPOFilter}
          renderItem={renderItem}
          loading={loading}
          refreshing={refreshing}
          onRefresh={onRefresh}
        />
      </View>
      <View
        style={{
          backgroundColor: colors.surface, // Changed from MaterialColors.surface
          paddingHorizontal: wp('2.5%'),
          paddingVertical: hp('0.5%'),
          position: 'absolute',
          right: 0,
          bottom: 0,
          left: 0,
        }}>
        {/* <AppButton
          Title="Add Items"
          OnPress={() => {
            if (isClosed) {
              Alert.alert('Purchase order Already Closed');
            } else {
              setModalVisible(!modalVisible);
            }
          }}
        /> */}

        <View style={{alignItems: 'center', flexDirection: 'row'}}>
          <FAB
            label="Add Items"
            position="bottomRight"
            onPress={() => {
              if (!isEnabled || isClosed) {
                Alert.alert('Purchase order Already Closed');
              } else {
                setModalVisible(!modalVisible);
              }
            }}
          />

          {itemPO.length > 0 && (
            <View style={{marginRight: wp('8.5%')}}>
              <FAB
                label="Receive All"
                position="bottomRightEven"
                onPress={() => ReceiveAllItmes()}
              />
            </View>
          )}
        </View>
      </View>

      <View>
        <Modal
          animationType="fade"
          transparent={true}
          visible={modalVisible}
          onRequestClose={() => setModalVisible(false)}>
          <View style={styles.container}>
            <View
              style={{
                width: '95%',
                backgroundColor: colors.background,
              }}>
              <Header
                NavName="Add Items"
                Onpress={() => {
                  setVendorItemsFilter(vendorItems);
                  setModalVisible(false);
                  setshowLookup(false);
                  setSearchQueryForModal('');
                }}
                isProvid={true}
                isOption={true}
                Options={() => {
                  if (textInputRef.current) {
                    textInputRef.current.clear();
                    textInputRef.current.blur();
                  }

                  setSearchQuery('');
                  Keyboard.dismiss();
                  setTimeout(() => {
                    if (textInputRef.current) {
                      textInputRef.current.focus();
                    }
                  }, 200);

                  setCameraModal(!cameraModal);
                }}
              />
            </View>
            {/* search bar */}

            <View style={{paddingBottom: hp('1%')}}>
              <AppSearchWIthFilter
                OnSearch={onChangeAddItems}
                SearchValue={searchQueryForModal}
                OnSearchSet={() => setFilter(true)}
                isEnableFilter={isEnableFilter}
                Keyboardon={showLookup}
                textInputRef={textInputRef}
                onToggleLookup={value => toggleLookup(value)}
                OnSubmitEditing={handleDoneClickSub}
              />

              <Text
                style={{
                  fontSize: FontSizes.medium,
                  fontFamily: Fonts.OnestBold,
                  color: colors.text, // Changed from MaterialColors.text.primary
                  paddingVertical: hp('1%'),
                }}>{`Total Items: (${vendorItemsFilter.length || 0})`}</Text>
            </View>
            <View
              style={{
                borderRadius: 10,
                backgroundColor: MaterialColors.surface,
              }}>
              <View style={{height: '82%', backgroundColor: colors.background}}>
                <DataList
                  data={vendorItemsFilter}
                  keyExtractor={item => String(item.ItemNum)}
                  renderItem={renderModalItem}
                  loading={loading}
                />
              </View>
            </View>
          </View>
        </Modal>
      </View>

      <View style={{flex: 1}}>
        <AppFilter
          isVisible={filter}
          setIsVisble={setFilter}
          Department={text => setSelectedDepartment(text)}
          Vedor={text => setSelectedVendor(text)}
          Brand={text => setSelectedBrand(text)}
          Category={text => setSelectedSubCategory(text)}
          EnableFilter={setIsEnableFilter}
          selectedDepartment={selectedDepartment}
          selectedVendor={selectedVendor}
          selectedBrand={selectedBrand}
          selectedSubCategory={selectedSubCategory}
          isEnableFilter={isEnableFilter}
        />
      </View>
      <View>
        <Modal
          animationType="fade"
          transparent={true}
          visible={modal}
          onRequestClose={() => setModal(false)}>
          <View style={styles.modalContainer}>
            <View
              style={{
                backgroundColor: colors.background, // Changed from Backround
                width: '93%',
                height: '80%',
                borderRadius: 15,
                paddingVertical: 10,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  paddingHorizontal: wp('2.5%'),
                  paddingVertical: hp('1.5%'),
                }}>
                <Text
                  style={{
                    fontFamily: Fonts.OnestBold,
                    fontSize: hp('2.5%'),
                    color: colors.text, // Added theme color
                  }}>
                  Update Quanity
                </Text>

                <TouchableOpacity
                  style={{paddingLeft: 10}}
                  onPress={() => setModal(false)}>
                  <AntDesign
                    name="closecircle"
                    color={'tomato'}
                    size={hp('4%')}
                  />
                </TouchableOpacity>
              </View>

              <View style={{paddingHorizontal: 10}}>
                <Text
                  style={{
                    fontFamily: Fonts.OnestBold,
                    fontSize: hp('1.9%'),
                    color: colors.text, // Added theme color
                  }}>
                  Item Number: {selectPoItems?.ItemNum}
                </Text>
                <Text
                  style={{
                    fontFamily: Fonts.OnestBold,
                    fontSize: hp('1.9%'),
                    color: colors.text, // Added theme color
                  }}>
                  Item Name: {selectPoItems?.ItemName}
                </Text>
                <Text
                  style={{
                    fontFamily: Fonts.OnestBold,
                    fontSize: hp('1.9%'),
                    color: colors.text, // Added theme color
                  }}>
                  Ordered: {selectPoItems?.Quan_Ordered}
                </Text>
              </View>

              <View
                style={{
                  paddingHorizontal: wp('2.5%'),
                  position: 'absolute',
                  right: 0,
                  left: 0,
                  bottom: 0,
                  marginBottom: 10,
                }}>
                <AppTextInput
                  PlaceHolder="Enter Quanity"
                  Title="Update Quanity"
                  Value={updateQty}
                  onChangeText={text => setUpdateQty(text)}
                  isRequired={true}
                />
                <AppButton
                  Title="Update Qty"
                  OnPress={() => UpdateLatestQty()}
                />
              </View>
            </View>
          </View>
        </Modal>
      </View>

      <AppLoader modalVisible={appLoader} setModalVisible={setAppLoader} />

      <Modal
        animationType="slide"
        transparent={true}
        visible={camera}
        onRequestClose={() => setCamera(!camera)}>
        <View style={{width: '100%', height: '100%'}}>
          <AppScanner
            codeScanner={codeScannerMain}
            onClose={() => {
              setCamera(false);
              textInputRef2?.current?.focus();
            }}
          />
        </View>
      </Modal>

      <Modal
        animationType="slide"
        transparent={true}
        visible={cameraModal}
        onRequestClose={() => setCameraModal(!cameraModal)}>
        <View style={{width: '100%', height: '100%'}}>
          <AppScanner
            codeScanner={codeScannerModal}
            onClose={() => {
              setCameraModal(false);
              textInputRef?.current?.focus();
            }}
          />
        </View>
      </Modal>
    </View>
  );
};

export default PurchaseOrderItem;
