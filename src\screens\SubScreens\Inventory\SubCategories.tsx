import {
  View,
  Text,
  Alert,
  TouchableOpacity,
  Keyboard,
  KeyboardAvoidingView,
  SafeAreaView,
  Platform,
  StyleSheet,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import Header from '../../../components/Inventory/Header';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import AppButton from '../../../components/Inventory/AppButton';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import DataList from '../../../components/Inventory/AppList';
import {SubCategoryAdd} from '../../../server/types';
import {
  GetAllItems,
  GetCommonLatestID,
  GetItemsParamsNoFilterNoReturn,
  showAlert,
} from '../../../utils/PublicHelper';
import {getInventoryPort, getLotteryPort} from '../../../server/InstanceTypes';
import {createItem, deleteItem} from '../../../server/service';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {Fonts, FontSizes} from '../../../styles/fonts';
import {MaterialColors} from '../../../constants/MaterialColors';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

const SubCategories = () => {
  const [addUnitType, setAddUnitType] = useState<string>('');
  const [unitTypes, setUnitTypes] = useState<SubCategoryAdd[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [keyboardVisible, setKeyboardVisible] = useState<boolean>(false);
  useEffect(() => {
    getAllUnitTypes();
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => setKeyboardVisible(true),
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => setKeyboardVisible(false),
    );
    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const getAllUnitTypes = async () => {
    console.log('SubCategories 1');
    GetAllItems<SubCategoryAdd[]>(
      (await getLotteryPort()).toString(),
      '/GetAllSubCategories',
      setUnitTypes,
      setLoading,
    );
  };

  const addUnitTypes = async () => {
    if (!addUnitType || addUnitType === '') {
      Alert.alert('Please Enter the SubCategory');
      return;
    }
    const checkExists = await GetItemsParamsNoFilterNoReturn(
      (await getLotteryPort()).toString(),
      '/GetSubCategory/:SubCategory',
      {SubCategory: addUnitType},
    );

    if (Array.isArray(checkExists) && checkExists.length === 0) {
      const UnitID = await GetCommonLatestID(
        (await getLotteryPort()).toString(),
        '/GetSubCategoryID',
      );

      const AddUnitTypes: SubCategoryAdd = {
        ID: UnitID?.toString(),
        SubCategory: addUnitType,
      };

      const result = await createItem(
        (await getLotteryPort()).toString(),
        '/createsubcategory',
        AddUnitTypes,
      );

      if (result) {
        console.log('SubCategories 2');

        GetAllItems<SubCategoryAdd[]>(
          (await getLotteryPort()).toString(),
          '/GetAllSubCategories',
          setUnitTypes,
          setLoading,
        );
        setAddUnitType('');
        Alert.alert('SubCategory Added');
      }
    } else {
      Alert.alert('SubCategory Already Exists');
    }
  };

  const removeUnitTypes = async (RemoveUnitTypes: SubCategoryAdd) => {
    showAlert('Are you sure you want to delete SubCategory?')
      .then(async result => {
        if (result) {
          const result = await deleteItem(
            (await getLotteryPort()).toString(),
            '/DeleteSubCategory/:ID',
            {ID: RemoveUnitTypes.ID},
          );
          if (result) {
            const updatedArraySel = unitTypes.filter(
              item => item.ID !== RemoveUnitTypes.ID,
            );
            setUnitTypes(updatedArraySel);
          }
        }
      })
      .catch(error => {
        console.error('Error showing alert', error);
      });
  };

  const renderItemSkus = ({item}: {item: SubCategoryAdd}) => (
    <View style={styles.categoryCard}>
      <Text style={styles.categoryName}>{item.SubCategory}</Text>
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => removeUnitTypes(item)}>
        <MaterialCommunityIcons name="delete" color={colors.error} size={16} />
      </TouchableOpacity>
    </View>
  );

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    safeArea: {
      flex: 1,
      justifyContent: 'space-between',
      paddingHorizontal: wp('2.5%'),
    },
    listContainer: {
      flex: 1,
    },
    categoryCard: {
      backgroundColor: colors.card,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: wp('4%'),
      paddingVertical: hp('1.8%'),
      marginBottom: hp('1.2%'),
      borderRadius: 12,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: isDark ? 0.3 : 0.15,
      shadowRadius: 2.5,
      elevation: 2,
    },
    categoryName: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.text,
    },
    deleteButton: {
      backgroundColor: colors.error,
      padding: 8,
      borderRadius: 8,
    },
    inputContainer: {
      backgroundColor: colors.surface,
      padding: hp('2%'),
      borderRadius: 12,
      marginTop: hp('1%'),
      marginBottom: hp('1%'),
      elevation: 2,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: isDark ? 0.3 : 0.15,
      shadowRadius: 2.5,
    },
  });

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 60 : 30}>
      <SafeAreaView style={styles.safeArea}>
        <View
          style={[
            styles.listContainer,
            {height: keyboardVisible ? hp('30%') : hp('60%')},
          ]}>
          <Header NavName="SubCategories" />
          <DataList
            data={unitTypes}
            renderItem={renderItemSkus}
            loading={loading}
          />
        </View>

        <View style={styles.inputContainer}>
          <AppTextInput
            PlaceHolder="Enter SubCategory"
            Title="SubCategory"
            Value={addUnitType}
            onChangeText={text => setAddUnitType(text)}
          />
          <AppButton Title="Add SubCategory" OnPress={() => addUnitTypes()} />
        </View>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

export default SubCategories;
