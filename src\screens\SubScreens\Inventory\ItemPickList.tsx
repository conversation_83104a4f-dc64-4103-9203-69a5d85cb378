import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  Keyboard,
  Platform,
} from 'react-native';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Backround, SecondaryHint} from '../../../constants/Color';
import Header from '../../../components/Inventory/Header';
import Search from '../../../components/Inventory/Search';
import AppButton from '../../../components/Inventory/AppButton';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import ItemPickListCart from '../../../components/Inventory/ItemPickListCart';
import {
  GetAllItemsWithFilter,
  GetItemsParamsNoFilterNoReturn,
  handleSearch,
  showAlert,
  showAlertOK,
  updateData,
} from '../../../utils/PublicHelper';
import {Invoice_OnHold, Invoice_Totals} from '../../../server/types';
import DataList from '../../../components/Inventory/AppList';
import {useFocusEffect} from '@react-navigation/native';
import {getInventoryPort} from '../../../server/InstanceTypes';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {applyDefaultsInvoiceTotals} from '../../../Validator/Inventory/Barcode';
import {deleteItem} from '../../../server/service';
import {MaterialColors} from '../../../constants/MaterialColors';
import FAB from '../../../components/common/FAB';
import AppSearchWIthFilter from '../../../components/Inventory/AppSearchWIthFilter';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

type NavProps = {
  navigation: NativeStackNavigationProp<any>;
};

const ItemPickList: React.FC<NavProps> = ({navigation}) => {
  const [itemPickList, setItemPickList] = useState<Invoice_Totals[]>([]);
  const [filterPickList, setFilterPickList] = useState<Invoice_Totals[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState('');
  const textInputRef = useRef<TextInput>(null);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [showLookup, setshowLookup] = useState<boolean>(false);
  useFocusEffect(
    useCallback(() => {
      getInvoiceHoldID();
    }, []),
  );

  const getInvoiceHoldID = async () => {
    const data = await GetAllItemsWithFilter(
      (await getInventoryPort()).toString(),
      '/getinvoicehold',
      setItemPickList,
      setFilterPickList,
      setLoading,
    );

    if (data === undefined || !data) {
      showAlertOK(
        'Database Connection Falied, Please Check Your Database Configuration',
        'Connection Failed',
        'OK',
      );
      return;
    } else {
      setItemPickList(data);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    // setPage(1);

    try {
      await getInvoiceHoldID();
    } catch (error) {
      console.error('Error refreshing data:', error);
    }

    setRefreshing(false);
  };

  const onSearchChange = (text: string) => {
    setSearchQuery(text);
    handleSearch(
      text,
      itemPickList,
      ['OnHoldID', 'Invoice_Number'],
      setFilterPickList,
      setLoading,
    );
  };

  const removePO = (RemovePO: Invoice_Totals) => {
    showAlert(
      'Are you sure you want to delete this on-hold order? All associated data will be lost.',
      'Delete On-Hold Order?',
    )
      .then(async result => {
        if (result) {
          updateInvoiceTotals(RemovePO);
        }
      })
      .catch(error => {
        console.error('Error showing alert', error);
      });
  };

  const updateInvoiceTotals = async (pickListItem: Invoice_Totals) => {
    try {
      const pickListData: Partial<Invoice_Totals> = {
        Invoice_Number: pickListItem.Invoice_Number,
        ReferenceInvoiceNumber: pickListItem.Invoice_Number.toString(),
        Orig_OnHoldID: pickListItem.Orig_OnHoldID,
        Store_ID: pickListItem.Store_ID,
        Cashier_ID: pickListItem.Cashier_ID,
        CustNum: pickListItem.CustNum,
        DateTime: pickListItem.DateTime,
        Total_Price: pickListItem.Total_Price,
        Total_Cost: pickListItem.Total_Cost,
        Total_Tax1: pickListItem.Total_Tax1,
        Grand_Total: pickListItem.Grand_Total,
        Station_ID: pickListItem.Station_ID,
        Payment_Method: pickListItem.Payment_Method,
        Status: 'C',
        Taxed_1: pickListItem.Taxed_1,
        Taxed_Sales: pickListItem.Taxed_Sales,
        Dirty: pickListItem.Dirty,
        CourseOrderingProgress: pickListItem.CourseOrderingProgress,
        Total_UndiscountedSale: pickListItem.Total_UndiscountedSale,
      };

      const applyDefult = applyDefaultsInvoiceTotals(pickListData);

      const result = await updateData<Invoice_Totals>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefult,
        endpoint: '/updateinvoice',
      });
      if (result) {
        const result = await deleteItem(
          (await getInventoryPort()).toString(),
          '/DeleteOnHold/:Invoice_Number',
          {Invoice_Number: pickListItem.Invoice_Number},
        );
        GetAllItemsWithFilter(
          (await getInventoryPort()).toString(),
          '/getinvoicehold',
          setItemPickList,
          setFilterPickList,
          setLoading,
        );
      }
    } catch (error) {}
  };

  const renderItem = ({item}: {item: Invoice_Totals}) => {
    return (
      <View>
        <ItemPickListCart
          key={item.Invoice_Number}
          Reference={item?.OnHoldID}
          CreatedDate={new Date(item.DateTime).toISOString().split('T')[0]}
          OnPress={() =>
            navigation.navigate('AddItemPickList', {
              ItemData: item,
              VALIDREF: item.OnHoldID,
            })
          }
          GrandTotal={Number(item.Total_Price.toFixed(2))}
          OnPressDelete={() => removePO(item)}
        />
      </View>
    );
  };

  const toggleLookup = useCallback(async (value: boolean) => {
    setshowLookup(value);
    setSearchQuery('');
    GetAllItemsWithFilter(
      (await getInventoryPort()).toString(),
      '/getinvoicehold',
      setItemPickList,
      setFilterPickList,
      setLoading,
    );
    if (Platform.OS === 'android') {
      if (value) {
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.blur();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 50);
          }
        }, 50);
      } else {
        setSearchQuery('');
        Keyboard.dismiss();
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.blur();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 50);
          }
        }, 50);
      }
      return;
    }

    // iOS handling
    if (value) {
      setTimeout(() => {
        textInputRef.current?.focus();
      }, 100);
    } else {
      setSearchQuery('');
      Keyboard.dismiss();
    }
  }, []);

  const handleDoneClick = async () => {
    setshowLookup(false);
    GetAllItemsWithFilter(
      (await getInventoryPort()).toString(),
      '/getinvoicehold',
      setItemPickList,
      setFilterPickList,
      setLoading,
    );
    setSearchQuery('');
    Keyboard.dismiss();
    setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.blur();
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.focus();
          }
        }, 50);
      }
    }, 50);
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  return (
    <View
      style={{
        backgroundColor: colors.background, // Changed from Backround
        flex: 1,
        justifyContent: 'space-between',
      }}>
      <View style={{paddingHorizontal: wp('2.5%')}}>
        <Header NavName="Pick & Hold" />
        {/* <TextInput
          onChangeText={text => onSearchChange(text)}
          placeholder="input"
          value={searchQuery}
          style={{
            width: '100%',
            paddingVertical: 10,
            paddingHorizontal: 8,
          }}
        /> */}

        <AppSearchWIthFilter
          OnSearch={onSearchChange}
          SearchValue={searchQuery}
          Keyboardon={showLookup}
          textInputRef={textInputRef}
          onToggleLookup={toggleLookup}
          IsFilter={false}
          OnSubmitEditing={() => handleDoneClick()}
        />

        <DataList
          data={filterPickList}
          renderItem={renderItem}
          loading={loading}
          Hight="77%"
          refreshing={refreshing}
          onRefresh={onRefresh}
        />
      </View>
      <View
        style={{
          paddingHorizontal: wp('2.5%'),
          backgroundColor: colors.surface, // Changed from MaterialColors.surface
          elevation: 12,
          shadowColor: colors.shadow, // Changed from '#000'
          shadowOffset: {width: 0, height: -4},
          shadowOpacity: isDark ? 0.3 : 0.15, // Dynamic shadow opacity
          shadowRadius: 6,
          borderTopWidth: 0,
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
          paddingVertical: hp('1%'),
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
        }}>
        <FAB
          label={'Create New PicKList'}
          position="bottomRight"
          onPress={() => navigation.navigate('CreatePickList')}
        />
      </View>
    </View>
  );
};

export default ItemPickList;
