import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleSheet,
  TextInput,
  Alert,
  Keyboard,
  Platform,
  ActivityIndicator,
} from 'react-native';
import React, {memo, useCallback, useEffect, useRef, useState} from 'react';
import {
  Backround,
  Primary,
  Secondary,
  SecondaryHint,
} from '../../../constants/Color';
import Header from '../../../components/Inventory/Header';
import AppButton from '../../../components/Inventory/AppButton';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {
  BrandOrSubCategory,
  Brands,
  Department,
  Get_Max_ID,
  Inventory,
  Inventory_Filter,
  Invoice_Itemized,
  Invoice_Totals,
  SubCategories,
  Vendor,
} from '../../../server/types';
import {
  calculatePriceWithVAT1,
  createData,
  findArrayDifference,
  findDifference,
  GetAllItems,
  GetAllItemsWithFilter,
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
  GetItemsWithParams,
  handleSearch,
  onSearchChange_Common,
  showAlert,
  showAlertOK,
  updateData,
} from '../../../utils/PublicHelper';
import DataList from '../../../components/Inventory/AppList';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {deleteItem, fetchSingleItem} from '../../../server/service';
import CustomCheckbox from '../../../components/Inventory/CustomCheckbox';
import {
  applyDefaultsInvoiceItemized,
  applyDefaultsInvoiceTotals,
} from '../../../Validator/Inventory/Barcode';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import {getInventoryPort} from '../../../server/InstanceTypes';
import AppScanner from '../../../components/Inventory/AppScanner';
import {useCodeScanner} from 'react-native-vision-camera';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import AppFocus from '../../../components/Inventory/AppFocus';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../../styles/fonts';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AppSearchWIthFilter from '../../../components/Inventory/AppSearchWIthFilter';
import AppFilter from '../../../components/Inventory/AppFilter';
import Search from '../../../components/Inventory/Search';
import {MaterialColors} from '../../../constants/MaterialColors';
import FAB from '../../../components/common/FAB';
import LottieView from 'lottie-react-native';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

type BarcodeScreenRouteProp = RouteProp<any, 'AddItemPickList'>;
const AddItemtoPickList: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [pickListItem, setPickListItem] = useState<Invoice_Totals>(
    route.params?.ItemData,
  );
  const [validRef, setValidRef] = useState<string>(route.params?.VALIDREF);
  const [invoiceItem, setInvoiceItem] = useState<Invoice_Itemized[]>([]);
  const [initial, setInitial] = useState<Invoice_Itemized[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [checkedState, setCheckedState] = useState({});
  const [selectedItems, setSelectedItems] = useState<Inventory[]>([]);
  const [inputValues, setInputValues] = useState<
    {itemNum: string; value: string}[]
  >([]);
  const [scanner, setScanner] = useState<boolean>(false);
  const [success, setSuccess] = useState<boolean>(false);
  const textInputRef = useRef<TextInput>(null);
  const textInputRef2 = useRef<TextInput>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');

  const [totalCost, setTotalCost] = useState<number>(
    0 || route.params?.ItemData?.Total_Cost,
  );
  const [totalPrice, setTotalPrice] = useState<number>(
    0 || route.params?.ItemData?.Total_Price,
  );
  const [totalPriceVat, setTotalPriceVat] = useState<number>(
    0 || route.params?.ItemData?.Total_Tax1,
  );
  const [grandTotal, setGrandTotal] = useState<number>(
    0 || route.params?.ItemData?.Grand_Total,
  );
  const [camera, setCamera] = useState<boolean>(false);
  const [cameraModal, setCameraModal] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [showLookup, setshowLookup] = useState<boolean>(false);
  const [showLookupMain, setShowLookupMain] = useState<boolean>(false);
  const [isBarcodeScanned, setIsBarcodeScanned] = useState(true);
  const [barcode, setBarcode] = useState<string | null>(null);
  const [filter, setFilter] = useState<boolean>(false);
  const [isEnableFilter, setIsEnableFilter] = useState<boolean>(false);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [selectedVendor, setSelectedVendor] = useState<string>('');
  const [selectedBrand, setSelectedBrand] = useState<string>('');
  const [selectedSubCategory, setSelectedSubCategory] = useState<string>('');

  const [brandorSubCat, SetBrandSubOrCat] = useState<BrandOrSubCategory[]>([]);

  const [getBrands, setGetBrands] = useState<Brands[]>([]);
  const [getSubCategories, setGetSubCategories] = useState<SubCategories[]>([]);

  // Variables needed for modal functionality
  const [inventoryData, setInventoryData] = useState<Inventory_Filter[]>([]);
  const [filteredData, setFilteredData] = useState<Inventory_Filter[]>([]);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(20);
  const [displayData, setDisplayData] = useState<Inventory_Filter[]>([]);

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const InventoryModalItem = memo(
    ({
      item,
      onPress,
    }: {
      item: Inventory_Filter;
      onPress: (item: Inventory_Filter) => void;
    }) => {
      return (
        <TouchableOpacity
          style={{
            backgroundColor: colors.card, // was MaterialColors.surface
            marginVertical: hp('0.6%'),
            borderRadius: 15,
            paddingVertical: hp('1.5%'),
            paddingHorizontal: wp('2.5%'),
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            borderBottomWidth: 1,
            borderBottomColor: colors.border, // was MaterialColors.grey[200]
          }}
          onPress={() => onPress(item)}>
          <View>
            <Text
              style={{
                fontFamily: Fonts.OnestBold,
                fontSize: FontSizes.small,
                color: colors.text, // Add this
              }}>
              {item.ItemName}
            </Text>

            <Text
              style={{
                fontFamily: Fonts.OnestBold,
                fontSize: FontSizes.medium,
                color: colors.textSecondary, // was '#A1A1A1'
              }}>
              {`Cost: ${item.Cost?.toFixed(2)}`}
            </Text>
          </View>
          <Text
            style={{
              fontFamily: Fonts.OnestBold,
              fontSize: FontSizes.large,
              color: Primary,
            }}>
            ${item.Price?.toFixed(2) || '0.00'}
          </Text>
        </TouchableOpacity>
      );
    },
    (prevProps, nextProps) => {
      // Only re-render if the item ID changes
      return prevProps.item.ItemNum === nextProps.item.ItemNum;
    },
  );

  const handleBarcodeScan = (scannedBarcode: string) => {
    setBarcode(scannedBarcode);
    setIsBarcodeScanned(true);
  };

  // Pagination function needed for handleDoneClickSub
  const applyPagination = (data: Inventory_Filter[], currentPage: number) => {
    const endIndex = currentPage * pageSize;
    const paginatedItems = data.slice(0, endIndex);
    setDisplayData(paginatedItems);
  };

  useFocusEffect(
    useCallback(() => {
      setIsBarcodeScanned(false);
      setSearchQuery('');
      setshowLookup(false);
      setShowLookupMain(false);
      setTimeout(() => {
        if (textInputRef2.current) {
          textInputRef2.current.blur();
          setTimeout(() => {
            if (textInputRef2.current) {
              textInputRef2.current.focus();
            }
          }, 50);
        }
      }, 50);
      setTimeout(() => {
        if (textInputRef.current) {
          textInputRef.current.blur();
          setTimeout(() => {
            if (textInputRef.current) {
              textInputRef.current.focus();
            }
          }, 50);
        }
      }, 50);
    }, []),
  );

  // const toggleLookup = (checked: boolean) => {
  //   if (checked) {
  //     setshowLookup(checked);
  //     textInputRef?.current?.blur();
  //     setTimeout(() => {
  //       textInputRef?.current?.focus();
  //     }, 1200);
  //   } else {
  //     Keyboard.dismiss();
  //     setTimeout(() => {
  //       textInputRef?.current?.focus();
  //     }, 1200);
  //     setshowLookup(checked);
  //   }
  // };

  // const toggleLookupMain = (checked: boolean) => {
  //   if (checked) {
  //     setShowLookupMain(checked);
  //     textInputRef2?.current?.blur();
  //     setTimeout(() => {
  //       textInputRef2?.current?.focus();
  //     }, 1200);
  //   } else {
  //     Keyboard.dismiss();
  //     setShowLookupMain(checked);
  //   }
  // };
  const toggleLookupMain = useCallback((value: boolean) => {
    setShowLookupMain(value);
    setSearchQuery('');
    setInitial(invoiceItem);

    if (Platform.OS === 'android') {
      if (value) {
        if (textInputRef2.current) {
          textInputRef2.current.clear();
          textInputRef2.current.blur();
        }
        setTimeout(() => {
          if (textInputRef2.current) {
            textInputRef2.current.blur();
            setTimeout(() => {
              if (textInputRef2.current) {
                textInputRef2.current.focus();
              }
            }, 200);
          }
        }, 200);
        // setInitial(invoiceItem);
      } else {
        setSearchQuery('');
        Keyboard.dismiss();
        setTimeout(() => {
          if (textInputRef2.current) {
            textInputRef2.current.blur();
            setTimeout(() => {
              if (textInputRef2.current) {
                textInputRef2.current.focus();
              }
            }, 200);
          }
        }, 200);
        //setInitial(invoiceItem);
      }
      return;
    }

    // iOS handling
    if (value) {
      setTimeout(() => {
        textInputRef2.current?.focus();
      }, 100);
      setInitial(invoiceItem);
    } else {
      setSearchQuery('');
      Keyboard.dismiss();
      setInitial(invoiceItem);
    }
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await init();
    } catch (error) {
      console.error('Error refreshing data:', error);
    }
    setRefreshing(false);
  };

  useFocusEffect(
    useCallback(() => {
      init();
    }, []),
  );
  const init = async () => {
    GetItemsParamsNoFilter<Invoice_Itemized[]>(
      (await getInventoryPort()).toString(),
      '/getinvoiceholditems/:Invoice_Number',
      setInvoiceItem,
      {Invoice_Number: route.params?.ItemData?.Invoice_Number},
      false,
    );
    GetItemsParamsNoFilter<Invoice_Itemized[]>(
      (await getInventoryPort()).toString(),
      '/getinvoiceholditems/:Invoice_Number',
      setInitial,
      {Invoice_Number: route.params?.ItemData?.Invoice_Number},
      false,
    );
    GetItemsParamsNoFilter<Invoice_Totals>(
      (await getInventoryPort()).toString(),
      '/GetInvoiceTotal/:Invoice_Number',
      setPickListItem,
      {Invoice_Number: route.params?.ItemData?.Invoice_Number},
      true,
    );
  };

  useEffect(() => {
    if (invoiceItem.length > 0) {
      invoiceItem.forEach(item => {
        handleCheckboxChange(item, true);
      });
    }
  }, [invoiceItem]);

  useEffect(() => {
    if (success) {
      updateInvoiceTotals(false);
    }
  }, [totalCost, totalPrice, totalPriceVat, grandTotal]);

  const removeHoldItems = (ItemMized: Invoice_Itemized) => {
    showAlert('Are you sure you want to delete?')
      .then(async result => {
        if (result) {
          const qtyCost =
            Number(ItemMized.Quantity) > 1
              ? Number(ItemMized.CostPer) * Number(ItemMized.Quantity)
              : ItemMized.CostPer;
          const qtyPrice =
            Number(ItemMized.Quantity) > 1
              ? Number(ItemMized.PricePer) * Number(ItemMized.Quantity)
              : ItemMized.PricePer;
          const VatPrice =
            Number(calculatePriceWithVAT1(Number(qtyPrice))) - Number(qtyPrice);
          const GrandTotal = Number(VatPrice) + Number(qtyPrice);

          const delQtyCost = Number(pickListItem.Total_Cost) - Number(qtyCost);
          const delQtyPrice =
            Number(pickListItem.Total_Price) - Number(qtyPrice);
          const delQtyVat = Number(pickListItem.Total_Tax1) - Number(VatPrice);
          const delQtyGrand =
            Number(pickListItem.Grand_Total) - Number(GrandTotal);

          const result = await deleteItem(
            (await getInventoryPort()).toString(),
            '/DeleteOnHoldItems/:Invoice_Number/:ItemNum',
            {
              Invoice_Number: pickListItem?.Invoice_Number,
              ItemNum: ItemMized?.ItemNum,
            },
          );

          if (result.success) {
            if (invoiceItem.length < 2) {
              updateInvoiceTotals(true, 0, 0, 0, 0, 0, 0);
            } else {
              updateInvoiceTotals(
                true,
                Number(pickListItem.Total_Price) - qtyPrice,
                Number(pickListItem.Total_Cost) - qtyCost,
                Number(pickListItem.Total_Tax1) - VatPrice,
                Number(pickListItem.Grand_Total) - GrandTotal,
                Number(pickListItem.Total_Price) - qtyPrice,
                Number(pickListItem.Total_Price) - qtyPrice,
              );
            }
            GetItemsParamsNoFilter<Invoice_Itemized[]>(
              (await getInventoryPort()).toString(),
              '/getinvoiceholditems/:Invoice_Number',
              setInvoiceItem,
              {Invoice_Number: route.params?.ItemData?.Invoice_Number},
              false,
            );
            GetItemsParamsNoFilter<Invoice_Itemized[]>(
              (await getInventoryPort()).toString(),
              '/getinvoiceholditems/:Invoice_Number',
              setInitial,
              {Invoice_Number: route.params?.ItemData?.Invoice_Number},
              false,
            );
          }
        } else {
          console.log('Item will not be deleted');
        }
      })
      .catch(error => {
        console.error('Error showing alert', error);
      });
  };

  const handleCheckboxChange = (
    item: Inventory,
    isChecked: boolean,
    isCheckBox: boolean = false,
  ) => {
    if (isChecked) {
      // Update checked state
      setCheckedState(prevState => ({
        ...prevState,
        [String(item?.ItemNum)]: isChecked,
      }));
    } else {
      // Remove object by item.ItemNum when isCheckBox is true
      setCheckedState(prevState => {
        const newState = {...prevState};
        delete newState[String(item?.ItemNum)]; // Remove the key corresponding to ItemNum
        return newState;
      });
    }

    if (isCheckBox) {
      // Update selected items
      setSelectedItems((prevItems: Inventory[]) => {
        if (isChecked) {
          return [...prevItems, item];
        } else {
          return prevItems.filter(
            selected => selected?.ItemNum !== item?.ItemNum,
          );
        }
      });
    }
  };

  const saveOnHoldItems = async () => {
    try {
      showAlert('Are you sure you want to Add Items?')
        .then(async result => {
          if (result) {
            for (const inputs of inputValues) {
              const results = await findDifference(selectedItems, initial);
              for (const data of results) {
                if (inputs.itemNum === data.ItemNum) {
                  const qtyCost =
                    Number(inputs.value) > 1
                      ? Number(data.Cost) * Number(inputs.value)
                      : data.Cost;
                  const qtyPrice =
                    Number(inputs.value) > 1
                      ? Number(data.Price) * Number(inputs.value)
                      : data.Price;
                  setTotalCost(
                    prevTotalCost => prevTotalCost + Number(qtyCost),
                  );
                  const VatPrice =
                    Number(calculatePriceWithVAT1(Number(qtyPrice))) -
                    Number(qtyPrice);
                  setTotalPriceVat(
                    prevTotalVat => prevTotalVat + Number(VatPrice),
                  );
                  setTotalPrice(
                    prevTotalprice => prevTotalprice + Number(qtyPrice),
                  );
                  const GrandTotal = Number(VatPrice) + Number(qtyPrice);
                  setGrandTotal(
                    prevTotalprice => prevTotalprice + Number(GrandTotal),
                  );
                  const storeId = await AsyncStorage.getItem('STOREID');
                  const invoiceItemized: Partial<Invoice_Itemized> = {
                    Invoice_Number: pickListItem?.Invoice_Number,
                    ItemNum: data.ItemNum,
                    Quantity: Number(inputs.value),
                    CostPer: data.Cost,
                    PricePer: data.Price,
                    Tax1Per: 1,
                    DiffItemName: data.ItemName,
                    Store_ID: storeId === null ? '1001' : storeId,
                    origPricePer: data.Price,
                    Allow_Discounts: true,
                    KitchenQuantityPrinted: 1,
                    PricePerBeforeDiscount: data.Price,
                    SentToKitchen: true,
                    Tare: -1,
                  };
                  const applyDefault =
                    applyDefaultsInvoiceItemized(invoiceItemized);

                  setSuccess(true);
                  const createResult = await createData<Invoice_Itemized>({
                    baseURL: (await getInventoryPort()).toString(),
                    data: applyDefault,
                    endpoint: '/createitemized',
                  });
                  if (createResult) {
                    initial.push(data);
                    setSuccess(true);
                  } else {
                    setSuccess(false);
                    console.log('ERROR');
                  }
                }
              }
            }

            Alert.alert('Successfully added item: ');
            GetItemsParamsNoFilter<Invoice_Itemized[]>(
              (await getInventoryPort()).toString(),
              '/getinvoiceholditems/:Invoice_Number',
              setInvoiceItem,
              {Invoice_Number: pickListItem?.Invoice_Number},
            );
          }
        })
        .catch(error => {
          console.error('Error showing alert', error);
        });
    } catch (error) {
      console.log(error);
    }
  };

  const updateInvoiceTotals = async (
    isDelete?: boolean,
    Total_Price?: number,
    Total_Cost?: number,
    Total_Tax1?: number,
    Grand_Total?: number,
    Taxed_Sales?: number,
    Total_UndiscountedSale?: number,
  ) => {
    try {
      const pickListData: Partial<Invoice_Totals> = {
        Invoice_Number: route.params?.ItemData?.Invoice_Number,
        ReferenceInvoiceNumber:
          route.params?.ItemData?.Invoice_Number.toString(),
        Orig_OnHoldID: route.params?.ItemData?.Orig_OnHoldID,
        Store_ID: route.params?.ItemData?.Store_ID,
        Cashier_ID: route.params?.ItemData?.Cashier_ID,
        CustNum: route.params?.ItemData?.CustNum,
        DateTime: route.params?.ItemData?.DateTime,
        Total_Price: !isDelete ? totalPrice : Total_Price,
        Total_Cost: !isDelete ? totalCost : Total_Cost,
        Total_Tax1: !isDelete ? totalPriceVat : Total_Tax1,
        Grand_Total: !isDelete ? grandTotal : Grand_Total,
        Station_ID: route.params?.ItemData?.Station_ID,
        Payment_Method: route.params?.ItemData?.Payment_Method,
        Status: route.params?.ItemData?.Status,
        Taxed_1: route.params?.ItemData?.Taxed_1,
        Taxed_Sales: !isDelete ? totalPrice : Taxed_Sales,
        Dirty: route.params?.ItemData?.Dirty,
        CourseOrderingProgress: route.params?.ItemData?.CourseOrderingProgress,
        Total_UndiscountedSale: !isDelete ? totalPrice : Total_UndiscountedSale,
      };

      const applyDefult = applyDefaultsInvoiceTotals(pickListData);
      setPickListItem(applyDefult);

      const result = await updateData<Invoice_Totals>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefult,
        endpoint: '/updateinvoice',
      });
      if (result) {
        setPickListItem(applyDefult);
        // setSuccess(false)
      }
    } catch (error) {}
  };

  const handleInputChange = (value: string, itemNum: string) => {
    setInputValues(prevValues => {
      // Check if the item already exists in the array
      const existingItemIndex = prevValues.findIndex(
        item => item.itemNum === itemNum,
      );

      if (existingItemIndex !== -1) {
        // Update the value of the existing item
        const updatedValues = [...prevValues];
        updatedValues[existingItemIndex].value = value;
        return updatedValues;
      } else {
        // Add a new object if the item is not found
        return [...prevValues, {itemNum, value}];
      }
    });
  };

  const ScantoAddItemList = async (Barcode: string) => {
    const checkExists = invoiceItem.find(item => item.ItemNum === Barcode);

    if (
      !checkExists ||
      (Array.isArray(checkExists) && checkExists.length === 0)
    ) {
      handleBarcodeScan(Barcode);
      try {
        const getBarcode = await fetchSingleItem<Inventory>(
          (await getInventoryPort()).toString(),
          '/inventory',
          Barcode,
        );

        if (
          getBarcode === undefined ||
          getBarcode.length === 0 ||
          !getBarcode
        ) {
          showAlert('Barcode not Found, do you want to create new?')
            .then(async result => {
              if (result) {
                navigation.navigate('ItemType', {ItemData: Barcode});
              }
            })
            .catch(error => {
              console.error('Error showing alert', error);
            });
        } else {
          if (getBarcode[0].ItemType === 3) {
            navigation.navigate('ChoiceItem', {
              ItemData: getBarcode,
              isPickList: true,
              Total: pickListItem,
            });
          } else {
            const getInventory = filteredData.filter(
              item => item.ItemNum === Barcode,
            );
            navigation.navigate('AddPickListEachItems', {
              ItemData: getInventory[0],
              PickItem: pickListItem,
            });
          }
        }
      } catch (error) {
        console.log(error);
      }
    } else {
      Alert.alert('Item Already Added!');
    }
  };

  const onSearchChange = (text: string) => {
    setSearchQuery(text);
    if (text.trim() === '') {
      setFilteredData(inventoryData);
      setSearchQuery('');
      return;
    }
    setTimeout(async () => {
      handleSearch(
        text,
        inventoryData,
        ['ItemName', 'ItemNum'],
        setFilteredData,
        setLoading,
      );
      const foundItem = inventoryData.filter(item => item.ItemNum === text);

      if (foundItem.length > 0) {
        // openModalForItem(text);
      } else {
        const userConfirmed = await showAlert(
          'Item not found. Do you want to create a new item?',
        );
        if (userConfirmed) {
          navigation.navigate('Barcode'); // Navigate only if Yes is clicked
        }
      }
    }, 1000);
  };

  const onSearchInvoiceItems = async (text: string) => {
    setCamera(false);
    const getBarcode = await GetItemsParamsNoFilterNoReturn(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      {ItemNum: text},
    );
    setSearchQuery(getBarcode[0]?.ItemNum || text);

    if (text) {
      if (showLookupMain) {
        handleSearch(
          text,
          invoiceItem,
          ['DiffItemName', 'ItemNum'],
          setInitial,
          setLoading,
        );
      } else {
        if (Array.isArray(getBarcode) && getBarcode.length === 0) {
          showAlert('Item Not Found, Do You Want to Create as New Item?')
            .then(async result => {
              if (result) {
                navigation.navigate('ItemType', {
                  ItemData: getBarcode[0]?.ItemNum || text,
                });
              } else {
                if (textInputRef2.current) {
                  textInputRef2.current.clear();
                  textInputRef2.current.blur();
                }

                setSearchQuery('');
                Keyboard.dismiss();
                setTimeout(() => {
                  if (textInputRef2.current) {
                    textInputRef2.current.focus();
                  }
                }, 200);
              }
            })
            .catch(error => {
              console.error('Error showing alert', error);
            });
        } else {
          const isExists = isItemNumExist(
            Array.isArray(getBarcode) && getBarcode.length === 0
              ? text
              : getBarcode[0]?.ItemNum,
          );
          if (isExists) {
            handleSearch(
              getBarcode[0]?.ItemNum || text,
              invoiceItem,
              ['DiffItemName', 'ItemNum'],
              setInitial,
              setLoading,
            );
          } else {
            setModalVisible(false);
            navigation.navigate('AddPickListEachItems', {
              ItemData: getBarcode[0],
              PickItem: pickListItem,
            });
          }
        }
      }
    } else {
      setInitial(invoiceItem);
    }
  };

  const onChangeAddItems = async (text: string) => {
    setCameraModal(false);
    const getBarcode = await GetItemsParamsNoFilterNoReturn<Inventory_Filter>(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      {ItemNum: text},
    );
    setSearchQuery(getBarcode[0]?.ItemNum || text);

    if (showLookup) {
      handleSearch(
        text,
        filteredData,
        ['ItemName', 'ItemNum'],
        setDisplayData,
        setLoading,
      );
    } else {
      if (text) {
        if (Array.isArray(getBarcode) && getBarcode.length === 0) {
          const userConfirmed = await showAlert(
            'Item not found. Do you want to create a new item?',
          );
          if (userConfirmed) {
            navigation.navigate('ItemType', {
              ItemData: getBarcode[0]?.ItemNum || text,
            });
          } else {
            if (textInputRef.current) {
              textInputRef.current.clear();
              textInputRef.current.blur();
            }

            setSearchQuery('');
            setshowLookup(false);
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 200);
          }
        } else {
          const isExists = isItemNumExist(
            Array.isArray(getBarcode) && getBarcode.length === 0
              ? text
              : getBarcode[0]?.ItemNum,
          );

          if (isExists) {
            showAlertOK(
              `This Item is Already Exists In Current Pick List`,
              'Already Exists',
              'OK',
              () => {
                if (textInputRef.current) {
                  textInputRef.current.clear();
                  textInputRef.current.blur();
                }

                setSearchQuery('');
                setshowLookup(false);
                Keyboard.dismiss();
                setTimeout(() => {
                  if (textInputRef.current) {
                    textInputRef.current.focus();
                  }
                }, 200);
              },
            );
          } else {
            setModalVisible(false);
            navigation.navigate('AddPickListEachItems', {
              ItemData: getBarcode[0],
              PickItem: pickListItem,
            });
          }
        }
      } else {
        setDisplayData(filteredData);
      }
    }
  };
  const isItemNumExist = (itemNum: string) => {
    return invoiceItem.some(item => item.ItemNum === itemNum);
  };

  const addItemsToPickList = useCallback(
    (inventory: Inventory) => {
      // Make sure we're checking against the current invoiceItem state
      const isExists = isItemNumExist(inventory?.ItemNum);

      if (isExists) {
        Alert.alert('Item Already Exists');
      } else {
        setModalVisible(false);
        navigation.navigate('AddPickListEachItems', {
          ItemData: inventory,
          PickItem: pickListItem,
        });
      }
    },
    [invoiceItem, navigation, pickListItem],
  );
  const renderModalItem = ({item}: {item: Inventory}) => {
    const inputValue =
      inputValues.find(input => input.itemNum === item.ItemNum)?.value ||
      invoiceItem
        .find(input => input.ItemNum === item.ItemNum)
        ?.Quantity.toString() ||
      '';
    return (
      <TouchableOpacity
        style={{
          backgroundColor: colors.card, // was MaterialColors.surface
          marginVertical: hp('0.6%'),
          borderRadius: 15,
          paddingVertical: hp('1.5%'),
          paddingHorizontal: wp('2.5%'),
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottomWidth: 1,
          borderBottomColor: colors.border, // was MaterialColors.grey[200]
        }}>
        <View>
          <Text
            style={{
              fontFamily: Fonts.OnestBold,
              fontSize: FontSizes.small,
              color: colors.text, // Add this
            }}>
            {item.ItemName}
          </Text>

          <Text
            style={{
              fontFamily: Fonts.OnestBold,
              fontSize: FontSizes.medium,
              color: colors.textSecondary, // was '#A1A1A1'
            }}>
            {`Cost: ${item.Cost?.toFixed(2)}`}
          </Text>
        </View>
        <Text
          style={{
            fontSize: FontSizes.medium,
            fontFamily: Fonts.OnestBold,
            color: colors.text, // was MaterialColors.text.primary
            paddingVertical: hp('1%'),
          }}>
          {`Total Items: (${filteredData.length || 0})`}
        </Text>
      </TouchableOpacity>
    );
  };

  const renderItem = ({item}: {item: Invoice_Itemized}) => {
    return (
      <TouchableOpacity>
        <View
          style={{
            flex: 1,
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: colors.card, // was MaterialColors.surface
            paddingHorizontal: wp('2.5%'),
            paddingVertical: hp('1%'),
            borderRadius: 15,
            borderBottomWidth: 1,
            borderBottomColor: colors.border, // was MaterialColors.grey[200]
          }}>
          <View style={{gap: 2}}>
            <Text
              style={{
                fontSize: FontSizes.small,
                fontFamily: Fonts.OnestBold,
                color: colors.text, // Add this
              }}>
              {item.DiffItemName}
            </Text>
            <Text
              style={{
                fontSize: FontSizes.small,
                color: colors.textSecondary, // was '#A1A1A1'
                fontFamily: Fonts.OnestMedium,
              }}>
              Qty: {item.Quantity}
            </Text>

            <Text
              style={{
                fontSize: FontSizes.large,
                color: MaterialColors.primary.main,
                fontFamily: Fonts.OnestBold,
              }}>
              ${item.PricePer.toFixed(2)}
            </Text>
          </View>

          <View>
            <TouchableOpacity onPress={() => removeHoldItems(item)}>
              <MaterialCommunityIcons
                name="delete-outline"
                color={MaterialColors.error.main}
                size={28}
              />
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const codeScannerMain = useCodeScanner({
    codeTypes: [
      'qr',
      'ean-13',
      'upc-a',
      'ean-8',
      'upc-e',
      'code-128',
      'code-39',
      'code-93',
    ],
    onCodeScanned: codes => {
      if (codes.length > 0) {
        onSearchInvoiceItems(codes[0].value);
      } else {
        console.log('No valid barcode detected.');
      }
    },
  });

  const codeScannerModal = useCodeScanner({
    codeTypes: [
      'qr',
      'ean-13',
      'upc-a',
      'ean-8',
      'upc-e',
      'code-128',
      'code-39',
      'code-93',
    ],
    onCodeScanned: codes => {
      if (codes.length > 0) {
        onChangeAddItems(codes[0].value);
      } else {
        console.log('No valid barcode detected.');
      }
    },
  });

  const NavigationHandler = () => {
    if (invoiceItem.length <= 0) {
      showAlert(
        `You haven't added any items yet. Do you want to continue with the Pick & Hold process?`,
      )
        .then(async result => {
          if (result) {
          } else {
            updateInvoiceTotals_Valid(pickListItem);
          }
        })
        .catch(error => {
          console.error('Error showing alert', error);
        });
    } else {
      navigation.navigate('ItemPickList');
    }
  };

  const updateInvoiceTotals_Valid = async (pickListItem: Invoice_Totals) => {
    try {
      const pickListData: Partial<Invoice_Totals> = {
        Invoice_Number: pickListItem.Invoice_Number,
        ReferenceInvoiceNumber: pickListItem.Invoice_Number.toString(),
        Orig_OnHoldID: pickListItem.Orig_OnHoldID,
        Store_ID: pickListItem.Store_ID,
        Cashier_ID: pickListItem.Cashier_ID,
        CustNum: pickListItem.CustNum,
        DateTime: pickListItem.DateTime,
        Total_Price: pickListItem.Total_Price,
        Total_Cost: pickListItem.Total_Cost,
        Total_Tax1: pickListItem.Total_Tax1,
        Grand_Total: pickListItem.Grand_Total,
        Station_ID: pickListItem.Station_ID,
        Payment_Method: pickListItem.Payment_Method,
        Status: 'C',
        Taxed_1: pickListItem.Taxed_1,
        Taxed_Sales: pickListItem.Taxed_Sales,
        Dirty: pickListItem.Dirty,
        CourseOrderingProgress: pickListItem.CourseOrderingProgress,
        Total_UndiscountedSale: pickListItem.Total_UndiscountedSale,
      };

      const applyDefult = applyDefaultsInvoiceTotals(pickListData);

      const result = await updateData<Invoice_Totals>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefult,
        endpoint: '/updateinvoice',
      });
      if (result) {
        const result = await deleteItem(
          (await getInventoryPort()).toString(),
          '/DeleteOnHold/:Invoice_Number',
          {Invoice_Number: pickListItem?.Invoice_Number},
        );

        navigation.navigate('ItemPickList');
      }
    } catch (error) {}
  };

  const handleDoneClick = () => {
    if (textInputRef2.current) {
      textInputRef2.current.clear();
      textInputRef2.current.blur();
    }

    setSearchQuery('');
    setShowLookupMain(false);
    Keyboard.dismiss();
    setTimeout(() => {
      if (textInputRef2.current) {
        textInputRef2.current.focus();
      }
    }, 200);
    setInitial(invoiceItem);
  };

  const handleDoneClickSub = () => {
    if (textInputRef.current) {
      textInputRef.current.clear();
      textInputRef.current.blur();
    }

    setSearchQuery('');
    setshowLookup(false);
    Keyboard.dismiss();
    setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.focus();
      }
    }, 200);
    applyPagination(filteredData, page);
  };

  const styles = StyleSheet.create({
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: hp('10%'),
    },
    animationContainer: {
      borderRadius: 20,
      backgroundColor: 'transparent',
    },
    lottie: {
      height: 200,
      width: 200,
      backgroundColor: 'transparent',
    },
    emptyText: {
      fontSize: 16,
      color: colors.textSecondary, // was MaterialColors.text.secondary
      textAlign: 'center',
      marginTop: 20,
      fontFamily: Fonts.OnestMedium,
    },
    footer: {
      padding: 15,
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    footerText: {
      marginLeft: 10,
      fontFamily: Fonts.OnestMedium,
      color: colors.textSecondary, // was MaterialColors.text.secondary
    },
    container: {
      flex: 1,
      backgroundColor: colors.background, // was Backround
      paddingHorizontal: wp('2.5%'),
    },
    modalOverlay: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.5)', // Transparent black background
    },
    tableCell: {
      textAlign: 'left', // Ensures proper alignment for text
      fontSize: 16,
      paddingHorizontal: 5,
    },
    itemNum: {
      width: 150, // Fixed width for ItemNum column
    },
    itemName: {
      flex: 1, // Let ItemName take up the remaining space
    },
    price: {
      width: 80, // Fixed width for Price column
      textAlign: 'right', // Align price to the right
    },
    searchInput: {
      height: 40,
      borderColor: '#ccc',
      borderWidth: 1,
      borderRadius: 5,
      paddingLeft: 10,
      marginBottom: 10,
      marginHorizontal: 10,
    },
  });

  return (
    <View
      style={{
        backgroundColor: colors.background, // was Backround
        flex: 1,
        justifyContent: 'space-between',
      }}>
      <View style={{paddingHorizontal: wp('2.5%')}}>
        <Header
          NavName="Add Item to PickList"
          isProvid={true}
          Onpress={() => NavigationHandler()}
          isOption={true}
          Options={() => {
            if (textInputRef2.current) {
              textInputRef2.current.clear();
              textInputRef2.current.blur();
            }

            setSearchQuery('');
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef2.current) {
                textInputRef2.current.focus();
              }
            }, 200);
            setInitial(invoiceItem);
            setCamera(!camera);
          }}
        />

        {/* <Header NavName="Add Item to PickList" /> */}

        <View
          style={{
            backgroundColor: colors.card, // was MaterialColors.background
            paddingHorizontal: wp('2.5%'),
            paddingVertical: hp('2%'),
            gap: 5,
            borderRadius: 15,
          }}>
          <Text
            style={{
              fontSize: FontSizes.medium,
              fontFamily: Fonts.OnestBold,
              color: colors.text, // Add this
            }}>
            Refrence Number: {validRef}
          </Text>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <Text
              style={{
                fontSize: FontSizes.medium,
                fontFamily: Fonts.OnestMedium,
                color: MaterialColors.text.secondary,
              }}>
              {
                new Date(route.params?.ItemData?.DateTime)
                  .toISOString()
                  .split('T')[0]
              }
            </Text>
            <Text
              style={{
                fontSize: FontSizes.medium,
                fontFamily: Fonts.OnestMedium,
                color: MaterialColors.text.secondary,
              }}>
              Total Price:
              {pickListItem.Total_Price
                ? `$ ${pickListItem.Total_Price.toFixed(2)}`
                : 'N/A'}
            </Text>
          </View>
        </View>

        <View style={{paddingVertical: hp('2%')}}>
          {/* <Search
            textInputRef={textInputRef2}
            PlaceHolder="Search..."
            Value={searchQuery}
            onChange={onSearchInvoiceItems}
            keyboardON={showLookupMain}
          /> */}
          <AppSearchWIthFilter
            OnSearch={onSearchInvoiceItems}
            SearchValue={searchQuery}
            Keyboardon={showLookupMain}
            textInputRef={textInputRef2}
            onToggleLookup={toggleLookupMain}
            IsFilter={false}
            OnSubmitEditing={() => handleDoneClick()}
          />
        </View>

        {scanner && (
          <View>
            <View style={{marginVertical: 10}}>
              <TouchableOpacity onPress={() => ScantoAddItemList('00100')}>
                <Text>Scan</Text>
              </TouchableOpacity>
            </View>

            <View style={{marginVertical: 10}}>
              <AppFocus
                PlaceHolder="Scan Book Number"
                Title="Scan Book Number"
                onChangeText={value => {
                  ScantoAddItemList(value);
                }}
              />
            </View>
          </View>
        )}

        <View style={{}}>
          <View>
            <Text
              style={{
                fontSize: FontSizes.medium,
                fontFamily: Fonts.OnestBold,
                color: colors.text, // was MaterialColors.text.primary
                paddingVertical: hp('1%'),
              }}>
              {`Items Added: (${initial.length || 0})`}
            </Text>
          </View>
        </View>

        <DataList
          data={initial}
          loading={loading}
          renderItem={renderItem}
          Hight="58%"
          refreshing={refreshing}
          onRefresh={onRefresh}
        />
      </View>

      <View
        style={{
          position: 'absolute',
          right: 0,
          left: 0,
          bottom: 0,
          backgroundColor: colors.surface, // was MaterialColors.surface
          paddingHorizontal: wp('2.5%'),
          paddingVertical: hp('1%'),
        }}>
        <FAB
          label={'Add Items To List'}
          position="bottomRight"
          onPress={() =>
            navigation.navigate('AddItemsToListScreen', {
              pickListItem: pickListItem,
              invoiceItem: invoiceItem,
            })
          }
        />
      </View>

      <Modal
        animationType="slide"
        transparent={true}
        visible={camera}
        onRequestClose={() => setCamera(!camera)}>
        <View style={{width: '100%', height: '100%'}}>
          <AppScanner
            codeScanner={codeScannerMain}
            onClose={() => {
              setCamera(false);
              textInputRef2?.current?.focus();
            }}
          />
        </View>
      </Modal>

      <Modal
        animationType="slide"
        transparent={true}
        visible={cameraModal}
        onRequestClose={() => setCameraModal(!cameraModal)}>
        <View style={{width: '100%', height: '100%'}}>
          <AppScanner
            codeScanner={codeScannerModal}
            onClose={() => {
              setCameraModal(false);
              textInputRef?.current?.focus();
            }}
          />
        </View>
      </Modal>

      <View style={{flex: 1}}>
        <AppFilter
          isVisible={filter}
          setIsVisble={setFilter}
          Department={text => setSelectedDepartment(text)}
          Vedor={text => setSelectedVendor(text)}
          Brand={text => setSelectedBrand(text)}
          Category={text => setSelectedSubCategory(text)}
          EnableFilter={setIsEnableFilter}
          selectedDepartment={selectedDepartment}
          selectedVendor={selectedVendor}
          selectedBrand={selectedBrand}
          selectedSubCategory={selectedSubCategory}
          isEnableFilter={isEnableFilter}
        />
      </View>
    </View>
  );
};

export default AddItemtoPickList;
