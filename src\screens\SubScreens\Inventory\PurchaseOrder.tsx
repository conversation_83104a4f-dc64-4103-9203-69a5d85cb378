import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  Alert,
  Keyboard,
  Platform,
} from 'react-native';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Backround, SecondaryHint} from '../../../constants/Color';
import Header from '../../../components/Inventory/Header';
import Search from '../../../components/Inventory/Search';
import PurchaseOrderCart from '../../../components/Inventory/PurchaseOrderCart';
import AppButton from '../../../components/Inventory/AppButton';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {
  GetAllItems,
  GetAllItemsWithFilter,
  GetItemsWithParams,
  handleSearch,
  showAlertOK,
} from '../../../utils/PublicHelper';
import {PurchaseOrder, Vendor} from '../../../server/types';
import DataList from '../../../components/Inventory/AppList';
import AppDropDown from '../../../components/Inventory/AppDropDown';
import {getInventoryPort} from '../../../server/InstanceTypes';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import AntDesign from 'react-native-vector-icons/AntDesign';
import {Fonts} from '../../../styles/fonts';
import AppSearchWIthFilter from '../../../components/Inventory/AppSearchWIthFilter';
import {hasPermission} from '../../../utils/permissionHelper';
import {MaterialColors} from '../../../constants/MaterialColors';
import FAB from '../../../components/common/FAB';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';
type NavProps = {
  navigation: NativeStackNavigationProp<any>;
};

type CountItemRouteProp = RouteProp<any, 'PurchaseOrderItem'>;

const PurchaseOrders: React.FC<{
  route: CountItemRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [purchaseOrd, setPurchaseOrd] = useState<PurchaseOrder[]>([]);
  const [filterPO, setFilterPO] = useState<PurchaseOrder[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [action, SetAction] = useState('O');
  const [filter, setFilter] = useState<boolean>(false);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [selectedVendor, setSelectedVendor] = useState<string>('');
  const [isEnableFilter, setIsEnableFilter] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [showLookup, setshowLookup] = useState<boolean>(false);
  const Action = [
    {label: 'Open', value: 'O'},
    {label: 'Closed', value: 'C'},
  ];
  useFocusEffect(
    useCallback(() => {
      getPurOrder();
      getVendor();
    }, []),
  );
  useEffect(() => {
    let filtered = purchaseOrd;

    if (selectedVendor) {
      filtered = filtered.filter(item => item.Vendor_Number === selectedVendor);
    }

    if (action) {
      filtered = filtered.filter(item => item.Status === action);
    }

    setFilterPO(filtered);
  }, [purchaseOrd, selectedVendor, action]);

  useEffect(() => {
    if (selectedVendor) {
      setIsEnableFilter(true);
    } else {
      setIsEnableFilter(false);
    }
  }, [selectedVendor]);

  const getPurOrder = async () => {
    const data = await GetItemsWithParams(
      (await getInventoryPort()).toString(),
      '/getpurchaseorder/:POType',
      setPurchaseOrd,
      setFilterPO,
      setLoading,
      {POType: route?.params?.ItemData},
    );

    if (data === undefined || !data) {
      showAlertOK(
        'Database Connection Falied, Please Check Your Database Configuration',
        'Connection Failed',
        'OK',
      );
      return;
    }
  };
  const onRefresh = async () => {
    setRefreshing(true);
    // setPage(1);

    try {
      await getPurOrder();
      await getVendor();
    } catch (error) {
      console.error('Error refreshing data:', error);
    }

    setRefreshing(false);
  };

  const renderItem = ({item}: {item: PurchaseOrder}) => (
    <PurchaseOrderCart
      PONumber={item.PO_Number}
      Vendor={item.Company}
      Price={Number(item.Total_Cost.toFixed(2))}
      DateTime={new Date(item.DateTime).toISOString().split('T')[0]}
      OnPress={() => {
        if (route?.params?.ItemData === 0) {
          navigation.navigate('PurchaseOrderItem', {
            ItemData: item,
            POType: route?.params?.ItemData,
          });
        } else {
          navigation.navigate('ReturnToVendor', {
            ItemData: item,
            POType: route?.params?.ItemData,
            POTYPE: route?.params?.ItemData,
          });
        }
      }}
    />
  );

  const onSearchChange = (text: string) => {
    if (action === 'O') {
      const filteredPO = purchaseOrd.filter(order => order.Status === 'O');
      if (text === '') {
        setFilterPO(filteredPO);
        SetAction('O');
      } else {
        setSearchQuery(text);
        handleSearch(
          text,
          filteredPO,
          ['PO_Number', 'Company', 'Vendor_Number'],
          setFilterPO,
          setLoading,
        );
        SetAction('O');
      }
    } else {
      const filteredPO = purchaseOrd.filter(order => order.Status === 'C');
      if (text === '') {
        setFilterPO(filteredPO);
        SetAction('C');
      } else {
        setSearchQuery(text);
        handleSearch(
          text,
          filteredPO,
          ['PO_Number', 'Company', 'Vendor_Number'],
          setFilterPO,
          setLoading,
        );
        SetAction('C');
      }
    }
  };

  const onStatusFilter = (value: string) => {
    try {
      const filteredPO = purchaseOrd.filter(order => order.Status === value);
      setFilterPO(filteredPO);
      SetAction(value);
    } catch (error) {
      console.log(error);
    }
  };

  const getVendor = async () => {
    GetAllItems<Vendor[]>(
      (await getInventoryPort()).toString(),
      '/GetVendors',
      setVendors,
      setLoading,
    );
  };

  const vendorOptions = vendors.map(vent => ({
    label: vent.Company,
    value: vent.Vendor_Number,
  }));

  const textInputRef = useRef<TextInput>(null);
  const toggleLookup = useCallback(async (value: boolean) => {
    const data = await GetItemsWithParams(
      (await getInventoryPort()).toString(),
      '/getpurchaseorder/:POType',
      setPurchaseOrd,
      setFilterPO,
      setLoading,
      {POType: route?.params?.ItemData},
    );
    setshowLookup(value);
    setSearchQuery('');

    if (Platform.OS === 'android') {
      if (value) {
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.blur();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 50);
          }
        }, 50);
      } else {
        Keyboard.dismiss();
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.blur();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 50);
          }
        }, 50);
      }
      return;
    }

    // iOS handling
    if (value) {
      setTimeout(() => {
        textInputRef.current?.focus();
      }, 100);
    } else {
      onSearchChange('');
      setSearchQuery('');
      Keyboard.dismiss();
    }
  }, []);

  const handleDoneClick = async () => {
    const data = await GetItemsWithParams(
      (await getInventoryPort()).toString(),
      '/getpurchaseorder/:POType',
      setPurchaseOrd,
      setFilterPO,
      setLoading,
      {POType: route?.params?.ItemData},
    );
    setSearchQuery('');
    setshowLookup(false);
    Keyboard.dismiss();
    setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.blur();
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.focus();
          }
        }, 50);
      }
    }, 50);
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    modalBackdrop: {
      backgroundColor: 'rgba(0,0,0,0.5)',
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContainer: {
      backgroundColor: colors.background, // Changed from Backround
      width: '93%',
      height: '70%',
      borderRadius: 15,
      paddingVertical: 10,
    },
    modalHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('1.5%'),
    },
    modalTitle: {
      fontFamily: Fonts.OnestBold,
      fontSize: hp('2.5%'),
      color: colors.text, // Added theme color
    },
    closeButton: {
      paddingLeft: 10,
    },
    filterContent: {
      paddingHorizontal: wp('2.5%'),
      marginTop: 20,
    },
  });
  return (
    <View
      style={{
        backgroundColor: colors.background, // Changed from Backround
        flex: 1,
        justifyContent: 'space-between',
      }}>
      <View style={{paddingHorizontal: wp('2.5%')}}>
        <Header
          NavName={
            route?.params?.ItemData === 0
              ? 'Purchase Order'
              : route?.params?.ItemData === 1
              ? 'Return To Vendor'
              : 'Direct Purchase'
          }
        />
        {route?.params?.ItemData === 0 && (
          <AppDropDown
            label="Status"
            options={Action}
            selectedValue={action}
            onSelect={value => onStatusFilter(value)}
            isNotClear={true}
          />
        )}
        {/* <Search
          value={searchQuery}
          PlaceHolder="Search"
          onChange={onSearchChange}
          AutoFocus={true}
        /> */}

        <AppSearchWIthFilter
          OnSearch={onSearchChange}
          SearchValue={searchQuery}
          OnSearchSet={() => setFilter(true)}
          isEnableFilter={isEnableFilter}
          Keyboardon={showLookup}
          textInputRef={textInputRef}
          onToggleLookup={value => toggleLookup(value)}
          OnSubmitEditing={handleDoneClick}
        />

        <DataList
          data={filterPO}
          renderItem={renderItem}
          loading={loading}
          Hight="66%"
          refreshing={refreshing}
          onRefresh={onRefresh}
        />
      </View>

      <View
        style={{
          position: 'absolute',
          right: 0,
          left: 0,
          bottom: 0,
          paddingHorizontal: wp('2.5%'),
          paddingVertical: hp('1%'),
          backgroundColor: colors.surface, // Changed from MaterialColors.surface
        }}>
        {/* <AppButton
          Title={
            route.params?.ItemData === 0
              ? 'Create New Order'
              : route.params?.ItemData === 1
              ? 'Create Vendor Return'
              : 'Create Direct Purchase'
          }
          OnPress={async () => {
            const isAuthorized = await hasPermission('CFA_HH_Create_PO');

            if (!isAuthorized) {
              Alert.alert(
                'You do not have permission to create purchase orders.',
              );
              return;
            }

            navigation.navigate('PurchaseOrderCreate', {
              POType: route?.params?.ItemData,
            });
          }}
        /> */}
      </View>
      <FAB
        label={
          route.params?.ItemData === 0
            ? 'Create New Order'
            : route.params?.ItemData === 1
            ? 'Create Vendor Return'
            : 'Create Direct Purchase'
        }
        position="bottomRight"
        onPress={async () => {
          const isAuthorized = await hasPermission('CFA_HH_Create_PO');

          if (!isAuthorized) {
            Alert.alert(
              'You do not have permission to create purchase orders.',
            );
            return;
          }

          navigation.navigate('PurchaseOrderCreate', {
            POType: route?.params?.ItemData,
          });
        }}
      />

      <Modal
        animationType="fade"
        transparent={true}
        visible={filter}
        onRequestClose={() => setFilter(false)}>
        <View style={styles.modalBackdrop}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Filter Items</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setFilter(false)}>
                <AntDesign
                  name="closecircle"
                  color={colors.error} // Changed from MaterialColors.error.main
                  size={hp('3%')}
                />
              </TouchableOpacity>
            </View>

            <View style={styles.filterContent}>
              <AppDropDown
                label="Vendors"
                options={vendorOptions}
                selectedValue={selectedVendor}
                onSelect={value => {
                  setIsEnableFilter(true);
                  setSelectedVendor(value);
                }}
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default PurchaseOrders;
