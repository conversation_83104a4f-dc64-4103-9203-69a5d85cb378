import {View, Text, TouchableOpacity, StyleSheet, Alert} from 'react-native';
import React, {useCallback, useState} from 'react';
import Header from '../../../components/Inventory/Header';
import SettingDrop from '../../../components/Inventory/SettingDrop';
import {Department} from '../../../server/types';
import {useFocusEffect} from '@react-navigation/native';
import {GetAllItems} from '../../../utils/PublicHelper';
import {getInventoryPort} from '../../../server/InstanceTypes';
import AsyncStorage from '@react-native-async-storage/async-storage';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {Fonts} from '../../../styles/fonts';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {MaterialColors} from '../../../constants/MaterialColors';
import {
  getOrgId,
  getAvailabilityInfo,
  createAvailabilityInfo,
  updateAvailabilityInfo,
} from '../../../utils/NotificationHelper';
import {AvailabilityInfo} from '../../../Types/GlobalTypes';

type NavProps = {
  navigation: NativeStackNavigationProp<any>;
};

const LOT_Setup: React.FC<NavProps> = ({navigation}) => {
  const [lottery, setLottery] = useState<Department[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedLottery, setSelectedLottery] = useState<string>('');
  const [serialNumberOrder, setSerialNumberOrder] = useState<string>('');
  const [isFirstTimeSetup, setIsFirstTimeSetup] = useState<boolean>(false);
  const [availabilityData, setAvailabilityData] =
    useState<AvailabilityInfo | null>(null);

  useFocusEffect(
    useCallback(() => {
      getAllDepartment();
      loadAvailabilityInfo();
    }, []),
  );

  const loadAvailabilityInfo = async () => {
    try {
      const orgId = await getOrgId();
      if (!orgId) {
        console.error('Organization ID not found');
        return;
      }

      const data = await getAvailabilityInfo(orgId);
      setAvailabilityData(data);

      if (data && data.is_ticket_ascending !== null) {
        // Data exists, set the order from server based on API response
        const order = data.is_ticket_ascending ? 'ascending' : 'descending';
        setSerialNumberOrder(order);
        await AsyncStorage.setItem('SERIAL_NUMBER_ORDER', order);
        setIsFirstTimeSetup(false);
      } else {
        // No data exists or is_ticket_ascending is null - show placeholder
        setIsFirstTimeSetup(true);
        setSerialNumberOrder(''); // Empty string to show placeholder
      }
    } catch (error) {
      console.error('Error loading availability info:', error);
      setIsFirstTimeSetup(true);
      setSerialNumberOrder(''); // Empty string to show placeholder
    }
  };

  const selectLotterDepartment = async (selectLottery: string) => {
    await AsyncStorage.setItem('LOTTERY_DEP_ID', selectLottery);
    setSelectedLottery(selectLottery);
  };

  const selectSerialNumberOrder = async (order: string) => {
    // Only allow selection during first time setup
    if (isFirstTimeSetup) {
      Alert.alert(
        'Warning',
        'Once you select the ticket order, you cannot change it later. You will need to contact the administrator to clean the database in order to change the order again. Are you sure you want to proceed?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Confirm',
            onPress: async () => {
              await saveTicketOrder(order);
            },
          },
        ],
      );
    } else {
      // Order already set, cannot change - show message
      Alert.alert(
        'Order Already Set',
        'The ticket order has already been set and cannot be changed. Please contact the administrator to clean the database if you need to change the order.',
        [{text: 'OK'}],
      );
    }
  };

  const saveTicketOrder = async (order: string) => {
    try {
      const orgId = await getOrgId();
      if (!orgId) {
        Alert.alert('Error', 'Organization ID not found');
        return;
      }

      const isAscending = order === 'ascending';

      if (availabilityData) {
        // Update existing data
        const updateData = {
          ...availabilityData,
          is_ticket_ascending: isAscending,
        };

        const success = await updateAvailabilityInfo(updateData);
        if (success) {
          await AsyncStorage.setItem('SERIAL_NUMBER_ORDER', order);
          setSerialNumberOrder(order);
          setIsFirstTimeSetup(false);
          Alert.alert('Success', 'Ticket order has been saved successfully.');
        } else {
          Alert.alert(
            'Error',
            'Failed to update ticket order. Please try again.',
          );
        }
      } else {
        // Create new data
        const createData = {
          org_id: orgId,
          is_ticket_ascending: isAscending,
          available_days: null,
          available_time: null,
          report_send_time: null,
        };

        const success = await createAvailabilityInfo(createData);
        if (success) {
          await AsyncStorage.setItem('SERIAL_NUMBER_ORDER', order);
          setSerialNumberOrder(order);
          setIsFirstTimeSetup(false);
          Alert.alert('Success', 'Ticket order has been saved successfully.');
        } else {
          Alert.alert(
            'Error',
            'Failed to save ticket order. Please try again.',
          );
        }
      }
    } catch (error) {
      console.error('Error saving ticket order:', error);
      Alert.alert('Error', 'An error occurred while saving the ticket order.');
    }
  };

  const getAllDepartment = async () => {
    GetAllItems<Department[]>(
      (await getInventoryPort()).toString(),
      '/GetDepartments',
      setLottery,
      setLoading,
    );

    const lotteryDepartment = await AsyncStorage.getItem('LOTTERY_DEP_ID');
    if (lotteryDepartment) {
      setSelectedLottery(lotteryDepartment);
    } else {
      selectLotterDepartment('');
    }
  };

  const lotteryOptions = lottery.map(vent => ({
    label: vent.Description,
    value: vent.Dept_ID,
  }));

  const serialOrderOptions = [
    {label: 'Ascending (000, 001, 002, etc.)', value: 'ascending'},
    {label: 'Descending (100, 099, 098, etc.)', value: 'descending'},
  ];

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Header NavName="Lottery Setup" />

        <View style={styles.section}>
          <SettingDrop
            label="Instant Game Department"
            options={lotteryOptions}
            selectedValue={selectedLottery}
            onSelect={value => selectLotterDepartment(value)}
          />
        </View>

        <View style={styles.section}>
          <SettingDrop
            label={`Serial Number Counting Order${
              !isFirstTimeSetup ? ' (Set - Contact Admin to Change)' : ''
            }`}
            options={serialOrderOptions}
            selectedValue={serialNumberOrder}
            onSelect={value => selectSerialNumberOrder(value)}
            isNotClear={true}
            disabled={!isFirstTimeSetup}
            placeholder="Select Counting Order"
          />
        </View>

        <View style={styles.section}>
          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => navigation.navigate('LotteryReason')}>
            <View style={styles.iconContainer}>
              <MaterialCommunityIcons
                name="clipboard-text-outline"
                color={MaterialColors.primary.dark}
                size={16}
              />
            </View>
            <Text style={styles.menuItemText}>Add Exception Reason Codes</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: MaterialColors.background,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    // marginVertical: 12,
  },
  menuItem: {
    backgroundColor: MaterialColors.surface,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: MaterialColors.grey[200],
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  iconContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    backgroundColor: MaterialColors.primary.light,
    marginRight: 12,
  },
  menuItemText: {
    fontFamily: Fonts.OnestBold,
    fontSize: 12,
    color: MaterialColors.text.primary,
  },
});

export default LOT_Setup;
