import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  Modal,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  TextStyle,
  ViewStyle,
  ActivityIndicator,
  TextInput,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../styles/fonts';
import {MaterialColors} from '../../constants/MaterialColors';
import LottieView from 'lottie-react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {deleteItem} from '../../server/service';
import {getInventoryPort} from '../../server/InstanceTypes';
import {showAlert} from '../../utils/PublicHelper';
import {useThemeColors} from '../../Theme/useThemeColors';
import {useTheme} from '../../Theme/ThemeContext';

interface CustomDropdownProps {
  label?: string;
  options: {label: string; value: string}[];
  selectedValue: string | number;
  onSelect: (value: string) => void;
  onCreate?: () => void;
  onShiftAll?: () => void;
  onChangeHookCall?: (boolean: boolean) => void;
  onChangeHookCall2?: (boolean: boolean) => void;
  error?: string;
  touched?: boolean;
  disabled?: boolean;
  isAdd?: boolean;
  isBackRoudColor?: boolean;
  isNotClear?: boolean;
  isRequired?: boolean;
  IsNotEdit?: boolean;
  isShiftReport?: boolean;
  IsDelete?: boolean;
  ItemNumber?: string;
  OptionLabel?: string;
  IsCreate?: boolean;
  IsTagAlong?: boolean;
  Height?: string;
  FixHeight?: string;
  enableSearch?: boolean;
  searchPlaceholder?: string;
}

const AppDropDown: React.FC<CustomDropdownProps> = ({
  label,
  options,
  selectedValue,
  onSelect,
  onCreate,
  onShiftAll,
  onChangeHookCall,
  onChangeHookCall2,
  error,
  touched,
  disabled,
  isAdd = false,
  isBackRoudColor = false,
  isNotClear = false,
  isRequired = false,
  IsNotEdit = false,
  isShiftReport,
  IsDelete = false,
  IsCreate = false,
  IsTagAlong = false,
  ItemNumber,
  OptionLabel,
  Height = '78%',
  FixHeight,
  enableSearch = true,
  searchPlaceholder = 'Search options...',
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [updatedOptions, setUpdatedOptions] = useState(options);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [filteredOptions, setFilteredOptions] = useState(options);

  let animation;

  const toggleDropdown = (): void => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      // Reset search when opening dropdown
      setSearchQuery('');
      setFilteredOptions(updatedOptions);
    }
  };

  useEffect(() => {
    setUpdatedOptions(options);
    setFilteredOptions(options);
  }, [options]);

  // Filter options based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredOptions(updatedOptions);
    } else {
      const filtered = updatedOptions.filter(option => {
        // Handle null, undefined, or non-string labels
        const label = option?.label;
        if (!label || typeof label !== 'string') {
          return false;
        }
        return label.toLowerCase().includes(searchQuery.toLowerCase());
      });
      setFilteredOptions(filtered);
    }
  }, [searchQuery, updatedOptions]);

  const handleSelect = (item: string): void => {
    onSelect(item);
    setIsOpen(false);
    setSearchQuery(''); // Reset search after selection
  };

  const handleClearSelection = (): void => {
    onSelect('');
    setIsOpen(false);
    setSearchQuery(''); // Reset search after clearing
  };

  const onDeleteItems = async (Value: any) => {
    showAlert('Are you sure you want to Delete?').then(async result => {
      if (result) {
        const updatedList = updatedOptions.filter(
          item => item.value !== Value.value,
        );

        if (IsCreate && IsTagAlong) {
          const existingData = await AsyncStorage.getItem('SetTagAlongs');
          const existingArray = existingData ? JSON.parse(existingData) : [];
          const updateLocalStorage = existingArray.filter(
            item => item.TagAlong_ItemNum !== Value.value,
          );

          await AsyncStorage.setItem(
            'SetTagAlongs',
            JSON.stringify(updateLocalStorage),
          );
        } else {
          await deleteItem(
            (await getInventoryPort()).toString(),
            '/DeleteTagAlone/:ItemNum/:TagAlong_ItemNum',
            {ItemNum: ItemNumber, TagAlong_ItemNum: Value.value},
          );
          if (onChangeHookCall) {
            onChangeHookCall(true);
          }
        }
        setUpdatedOptions(updatedList);

        if (selectedValue === Value.value) {
          onSelect('');
        }
      }
    });
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      marginVertical: hp('1%'),
      width: '100%',
      overflow: 'hidden',
    },
    labelContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: hp('1%'),
    },
    label: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestMedium,
      color: colors.text,
    },
    requiredStar: {
      color: colors.error,
      fontSize: FontSizes.large,
      marginLeft: 4,
    },
    addContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      width: '100%',
    },
    selectedValueContainer: {
      borderRadius: 8,
      paddingVertical: hp('1.5%'),
      paddingHorizontal: wp('3%'),
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.border,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: isDark ? 0.3 : 0.2,
      shadowRadius: 2,
      elevation: 2,
      width: '100%',
      maxWidth: '100%',
    },
    selectedValueContainerAdd: {
      borderRadius: 8,
      paddingVertical: hp('1.5%'),
      paddingHorizontal: wp('3%'),
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.border,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: isDark ? 0.3 : 0.2,
      shadowRadius: 2,
      elevation: 2,
      marginRight: wp('2%'),
      flex: 1,
      maxWidth: '85%',
    },
    selectedValueContainerAddBackgroud: {
      borderRadius: 8,
      paddingVertical: hp('1.5%'),
      paddingHorizontal: wp('3%'),
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.border,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: isDark ? 0.3 : 0.2,
      shadowRadius: 2,
      elevation: 2,
      marginRight: wp('2%'),
      flex: 1,
      maxWidth: '85%',
    },
    disabledContainer: {
      backgroundColor: colors.disabled,
      borderColor: colors.border,
    },
    selectedValue: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestMedium,
      color: colors.text,
      flex: 1,
      flexWrap: 'wrap',
    },
    selectedValueDisabled: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestMedium,
      color: colors.textSecondary,
      flex: 1,
      flexWrap: 'wrap',
    },
    dropdownIcon: {
      marginLeft: 8,
    },
    addButton: {
      backgroundColor: colors.background,
      width: wp('11.3%'),
      height: wp('11.3%'),
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 8,
      borderWidth: 1,
      borderColor: colors.border,
      marginRight: wp('2.5%'),
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: isDark ? 0.3 : 0.2,
      shadowRadius: 2,
      elevation: 2,
    },
    overlay: {
      flex: 1,
      justifyContent: 'flex-end',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    dropdown: {
      backgroundColor: colors.surface,
      width: '100%',
      maxHeight: hp('50%'),
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      overflow: 'hidden',
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: -3},
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 5,
      elevation: 5,
    },
    searchContainer: {
      padding: hp('1.5%'),
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      backgroundColor: colors.surface,
    },
    searchInput: {
      backgroundColor: colors.background,
      borderRadius: 8,
      paddingHorizontal: wp('3%'),
      paddingVertical: hp('1%'),
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestMedium,
      color: colors.text,
      borderWidth: 1,
      borderColor: colors.border,
    },
    clearOption: {
      padding: hp('2%'),
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      backgroundColor: colors.primary + '20',
    },
    clearOptionText: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestMedium,
      color: colors.primary,
      textAlign: 'center',
    },
    option: {
      padding: hp('1.8%'),
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      flexDirection: 'row',
      alignItems: 'center',
    },
    selectedOption: {
      backgroundColor: colors.primary + '20',
    },
    optionText: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestMedium,
      color: colors.text,
      flex: 1,
    },
    selectedOptionText: {
      fontFamily: Fonts.OnestBold,
      color: colors.primary,
    },
    checkIcon: {
      marginLeft: 10,
    },
    deleteButton: {
      padding: 8,
    },
    trashIcon: {
      marginLeft: 10,
    },
    errorText: {
      color: colors.error,
      fontSize: FontSizes.small,
      fontFamily: Fonts.OnestMedium,
      marginTop: 5,
    },
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: hp('3%'),
    },
    emptyText: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestMedium,
      color: colors.textSecondary,
      marginTop: hp('1%'),
    },
    lottie: {
      height: 120,
      width: 120,
      backgroundColor: 'transparent',
    },
    noResultsContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: hp('3%'),
    },
    noResultsText: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestMedium,
      color: colors.textSecondary,
      textAlign: 'center',
    },
  });

  return (
    <View style={styles.container}>
      {label && (
        <View style={styles.labelContainer}>
          <Text style={styles.label}>{label}</Text>
          {isRequired && <Text style={styles.requiredStar}>*</Text>}
        </View>
      )}

      <View style={isAdd ? styles.addContainer : undefined}>
        <TouchableOpacity
          activeOpacity={0.7}
          style={[
            isAdd
              ? isBackRoudColor
                ? styles.selectedValueContainerAddBackgroud
                : styles.selectedValueContainerAdd
              : styles.selectedValueContainer,
            {
              borderColor: error && touched ? colors.error : colors.card,
            },
            disabled && styles.disabledContainer,
          ]}
          onPress={!disabled ? toggleDropdown : () => {}}>
          <Text
            style={
              !disabled ? styles.selectedValue : styles.selectedValueDisabled
            }>
            {IsNotEdit
              ? `Tag Along Items: (${updatedOptions.length})`
              : selectedValue
              ? updatedOptions.find(option => option.value === selectedValue)
                  ?.label
              : OptionLabel || 'Select an option'}
          </Text>
          <Icon
            name="chevron-down"
            size={14}
            color={colors.textSecondary}
            style={styles.dropdownIcon}
          />
        </TouchableOpacity>

        {isAdd && (
          <TouchableOpacity
            activeOpacity={0.7}
            style={styles.addButton}
            onPress={onCreate}>
            <Ionicons
              name="add-circle-outline"
              color={colors.primary}
              size={hp('3.5%')}
            />
          </TouchableOpacity>
        )}
      </View>

      <Modal
        transparent={true}
        visible={isOpen}
        animationType="fade"
        onRequestClose={toggleDropdown}>
        <TouchableOpacity
          style={styles.overlay}
          activeOpacity={1}
          onPress={toggleDropdown}>
          <View style={styles.dropdown}>
            {/* Search Input */}

            {!isNotClear && (
              <TouchableOpacity
                style={styles.clearOption}
                onPress={handleClearSelection}>
                <Text style={styles.clearOptionText}>Clear Selection</Text>
              </TouchableOpacity>
            )}

            {enableSearch && (
              <View style={styles.searchContainer}>
                <TextInput
                  style={styles.searchInput}
                  placeholder={searchPlaceholder}
                  placeholderTextColor={colors.textSecondary}
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>
            )}
            {isShiftReport && (
              <TouchableOpacity
                style={styles.option}
                onPress={() => {
                  if (onShiftAll) {
                    onShiftAll();
                  }
                  setIsOpen(false);
                  setSearchQuery('');
                }}>
                <Text style={styles.optionText}>All</Text>
              </TouchableOpacity>
            )}

            {loading ? (
              <ActivityIndicator
                size="large"
                color={MaterialColors.primary.main}
              />
            ) : updatedOptions.length === 0 ? (
              <View style={styles.emptyContainer}>
                <LottieView
                  ref={animation}
                  style={styles.lottie}
                  source={require('../../assets/Lotties/Nodata.json')}
                  autoPlay
                  loop
                />
                <Text style={styles.emptyText}>No options available</Text>
              </View>
            ) : filteredOptions.length === 0 ? (
              <View style={styles.noResultsContainer}>
                <Icon name="search" size={40} color={colors.textSecondary} />
                <Text style={styles.noResultsText}>
                  No results found for "{searchQuery}"
                </Text>
              </View>
            ) : (
              <FlatList
                data={filteredOptions}
                renderItem={({item}) => (
                  <TouchableOpacity
                    style={[
                      styles.option,
                      selectedValue === item.value && styles.selectedOption,
                    ]}
                    activeOpacity={0.7}
                    onPress={() => (IsDelete ? {} : handleSelect(item.value))}>
                    <Text
                      style={[
                        styles.optionText,
                        selectedValue === item.value &&
                          styles.selectedOptionText,
                      ]}>
                      {item.label}
                    </Text>

                    {selectedValue === item.value && (
                      <Icon
                        name="check"
                        size={16}
                        color={colors.success}
                        style={styles.checkIcon}
                      />
                    )}

                    {IsDelete && (
                      <TouchableOpacity
                        onPress={() => onDeleteItems(item)}
                        style={styles.deleteButton}>
                        <Icon
                          name="trash"
                          size={20}
                          color={colors.error}
                          style={styles.trashIcon}
                        />
                      </TouchableOpacity>
                    )}
                  </TouchableOpacity>
                )}
                keyExtractor={item => item.value}
                keyboardShouldPersistTaps="handled"
              />
            )}
          </View>
        </TouchableOpacity>
      </Modal>

      {error && touched && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

export default AppDropDown;
