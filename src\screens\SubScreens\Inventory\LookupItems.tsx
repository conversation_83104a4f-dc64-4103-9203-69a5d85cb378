import React, {useCallback, useEffect, useRef, useState, memo} from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  Text,
  TouchableOpacity,
  Keyboard,
  TextInput,
  Alert,
  Platform,
  InteractionManager,
  FlatList,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Modal,
  BackHandler,
} from 'react-native';
import ChoieItemCard from '../../../components/Inventory/ChoieItemCard';
import Header from '../../../components/Inventory/Header';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {
  GetAllItems,
  GetAllItemsNoProps,
  GetAllItemsWithFilter,
  GetCommonLatestID,
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
  handleSearch,
  onSearchChange_Common,
  showAlertOK,
} from '../../../utils/PublicHelper';
import {
  AdditionalInfo,
  BrandAdd,
  BrandOrSubCategory,
  <PERSON><PERSON>,
  Department,
  Inventory,
  Inventory_Filter,
  InventoryVendor,
  SubCategories,
  SubCategoryAdd,
  Vendor,
} from '../../../server/types';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {getInventoryPort, getLotteryPort} from '../../../server/InstanceTypes';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {
  Backround,
  Primary,
  Secondary,
  SecondaryHint,
} from '../../../constants/Color';
import {Fonts, FontSizes} from '../../../styles/fonts';
import AppSearchWIthFilter from '../../../components/Inventory/AppSearchWIthFilter';
import AppFilter from '../../../components/Inventory/AppFilter';
import {
  hasPermission,
  navigateIfAuthorized,
} from '../../../utils/permissionHelper';
import {MaterialColors} from '../../../constants/MaterialColors';
import FAB from '../../../components/common/FAB';
import LottieView from 'lottie-react-native';
import AppLoader from '../../../components/Inventory/AppLoader';
import {createItem} from '../../../server/service';
import {ServerConnection} from '../../../Validator/Inventory/ServerValidation';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useCodeScanner} from 'react-native-vision-camera';
import AppScanner from '../../../components/Inventory/AppScanner';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

const {width, height} = Dimensions.get('window');

type BarcodeScreenRouteProp = RouteProp<any, 'Lookup'>;

// Memoized Item component
const InventoryItem = memo(
  ({item, onPress}: {item: Inventory_Filter; onPress: () => void}) => {
    return (
      <ChoieItemCard
        Barcode={item.ItemNum}
        Price={item.Price}
        Title={item.ItemName}
        inStock={item.In_Stock}
        OnPress={onPress}
      />
    );
  },
  (prevProps, nextProps) => {
    return prevProps.item.ItemNum === nextProps.item.ItemNum;
  },
);

// Empty list component
const EmptyListComponent = memo(
  ({
    searchQuery,
    IsfromLottery = false,
    styles,
  }: {
    searchQuery: string;
    IsfromLottery: boolean;
    styles: any;
  }) => (
    <View style={styles.emptyContainer}>
      <View style={styles.animationContainer}>
        <LottieView
          style={styles.lottie}
          source={require('../../../assets/Lotties/Nodata.json')}
          autoPlay
          loop
        />
      </View>
      <Text style={styles.emptyText}>
        {searchQuery
          ? 'No matching items found'
          : IsfromLottery
          ? 'No Lottery Games available'
          : 'No inventory items available'}
      </Text>
    </View>
  ),
);

// Footer loading component
const ListFooter = memo(
  ({loading, styles}: {loading: boolean; styles: any}) => {
    if (!loading) return null;
    return (
      <View style={styles.footer}>
        <ActivityIndicator size="small" />
        <Text style={styles.footerText}>Loading more items...</Text>
      </View>
    );
  },
);

const LookupItems: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  // State for all data and filtered data
  const [masterData, setMasterData] = useState<Inventory_Filter[]>([]);
  const [filteredData, setFilteredData] = useState<Inventory_Filter[]>([]);
  const [displayData, setDisplayData] = useState<Inventory_Filter[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [initialLoading, setInitialLoading] = useState<boolean>(true);
  const [filter, setFilter] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [debouncedQuery, setDebouncedQuery] = useState<string>('');

  // Pagination state
  const [page, setPage] = useState<number>(1);
  const ITEMS_PER_PAGE = 20;

  // Filter state
  const [inventoryVendor, setInventoryVendor] = useState<InventoryVendor[]>([]);
  const [inventoryAdditional, setInventoryAdditional] = useState<
    AdditionalInfo[]
  >([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<string>(
    route.params?.LOTTERYDEPT || '',
  );
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [selectedVendor, setSelectedVendor] = useState<string>('');
  const [selectedBrand, setSelectedBrand] = useState<string>('');
  const [selectedSubCategory, setSelectedSubCategory] = useState<string>('');
  const [brandorSubCat, SetBrandSubOrCat] = useState<BrandOrSubCategory[]>([]);
  const [getBrands, setGetBrands] = useState<Brands[]>([]);
  const [getSubCategories, setGetSubCategories] = useState<SubCategories[]>([]);
  const [isEnableFilter, setIsEnableFilter] = useState<boolean>(false);
  const [showLookup, setshowLookup] = useState<boolean>(false);
  const [camera, setCamera] = useState<boolean>(false);
  const [appLoader, setAppLoader] = useState<boolean>(true);

  const textInputRef = useRef<TextInput>(null);
  const flatListRef = useRef<FlatList>(null);

  const colors = useThemeColors();
  const {isDark} = useTheme();

  // Enhanced cleanup refs
  const abortControllerRef = useRef<AbortController | null>(null);
  const timeoutRefs = useRef<Set<NodeJS.Timeout>>(new Set());
  const isMountedRef = useRef(true);
  const isInitializedRef = useRef(false);
  const navigationListenerRef = useRef<(() => void) | null>(null);

  // Cleanup function for timeouts
  const clearAllTimeouts = useCallback(() => {
    timeoutRefs.current.forEach(timeout => {
      try {
        clearTimeout(timeout);
      } catch (error) {
        console.log('Error clearing timeout:', error);
      }
    });
    timeoutRefs.current.clear();
  }, []);

  // Safe setTimeout wrapper
  const safeSetTimeout = useCallback((callback: () => void, delay: number) => {
    const timeout = setTimeout(() => {
      if (isMountedRef.current) {
        try {
          callback();
        } catch (error) {
          console.log('Error in timeout callback:', error);
        }
      }
      // Remove from set after execution
      timeoutRefs.current.delete(timeout);
    }, delay);
    timeoutRefs.current.add(timeout);
    return timeout;
  }, []);

  // Enhanced cleanup on unmount
  useEffect(() => {
    isMountedRef.current = true;
    isInitializedRef.current = false;

    // Add navigation listener to detect when leaving screen
    const unsubscribe = navigation.addListener('beforeRemove', () => {
      // Cleanup when leaving screen
      isMountedRef.current = false;
      clearAllTimeouts();

      // Cancel any ongoing network requests
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }

      // Clear keyboard
      Keyboard.dismiss();

      // Clear refs
      if (textInputRef.current) {
        textInputRef.current.blur();
      }
    });

    navigationListenerRef.current = unsubscribe;

    return () => {
      isMountedRef.current = false;
      clearAllTimeouts();

      // Cancel any ongoing network requests
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }

      // Clear keyboard
      Keyboard.dismiss();

      // Clear navigation listener
      if (navigationListenerRef.current) {
        navigationListenerRef.current();
        navigationListenerRef.current = null;
      }
    };
  }, [clearAllTimeouts, navigation]);

  // Add back handler for Android
  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      () => {
        if (camera) {
          setCamera(false);
          return true;
        }
        if (filter) {
          setFilter(false);
          return true;
        }
        // Let default back behavior handle navigation
        return false;
      },
    );

    return () => {
      backHandler.remove();
    };
  }, [camera, filter]);

  // Initialize data on screen focus with better error handling
  useFocusEffect(
    useCallback(() => {
      let isActive = true;

      const initScreen = async () => {
        if (!isActive || !isMountedRef.current) return;

        try {
          // Prevent multiple initializations
          if (isInitializedRef.current) return;
          isInitializedRef.current = true;

          await checkServerConnection();
        } catch (error) {
          console.error('Error initializing screen:', error);
          if (isMountedRef.current) {
            setInitialLoading(false);
          }
        }
      };

      initScreen();

      return () => {
        isActive = false;
        isInitializedRef.current = false;
      };
    }, []),
  );

  const checkServerConnection = async () => {
    if (!isMountedRef.current) return;

    try {
      const inventoryPort = await getInventoryPort();
      const serverStatus = await ServerConnection(inventoryPort);

      if (!isMountedRef.current) return;

      if (!serverStatus) {
        showAlertOK(
          'Database Connection Failed, Please Check Your Database Configuration',
          'Connection Failed',
          'OK',
        );
        setInitialLoading(false);
        return;
      }

      setInitialLoading(true);

      // Use Promise.allSettled to handle concurrent operations better
      const operations = [
        invNoPage(),
        getInitDept(),
        getVendor(),
        getSubCatogoryOrBrand(),
        handleTransferExists(),
      ];

      await Promise.allSettled(operations);

      if (isMountedRef.current) {
        setFilter(false);
        setSearchQuery('');
        setPage(1);
        setshowLookup(false);

        // Focus the text input for scanning
        safeSetTimeout(() => {
          if (textInputRef.current && isMountedRef.current) {
            textInputRef.current.focus();
          }
        }, 100);
      }
    } catch (error) {
      console.error('Error in checkServerConnection:', error);
      if (isMountedRef.current) {
        setInitialLoading(false);
      }
    }
  };

  const handleTransferExists = async () => {
    if (!isMountedRef.current) return;

    try {
      await Promise.allSettled([
        handleUnitTypeTransfer(),
        handleAdditionalTransferBrand(),
        handleAdditionalTransferSubCat(),
      ]);
    } catch (error) {
      console.error('Error in handleTransferExists:', error);
    }
  };

  const handleAdditionalTransferBrand = async () => {
    if (!isMountedRef.current) return;

    try {
      const port = await getInventoryPort();
      const portLottery = await getLotteryPort();

      const [getExistBrands, getLotteryExists] = await Promise.allSettled([
        GetAllItemsNoProps(port, '/getExistsAdditionalBrand'),
        GetAllItemsNoProps(portLottery, '/GetAllBrands'),
      ]);

      if (!isMountedRef.current) return;

      const existBrands =
        getExistBrands.status === 'fulfilled' ? getExistBrands.value : [];
      const lotteryExists =
        getLotteryExists.status === 'fulfilled' ? getLotteryExists.value : [];

      if (lotteryExists === undefined || lotteryExists.length === 0) {
        if (existBrands.length > 0) {
          for (const item of existBrands) {
            if (!isMountedRef.current) break;

            const UnitID = await GetCommonLatestID(
              (await getLotteryPort()).toString(),
              '/GetBrandID',
            );

            const AddUnitTypes: BrandAdd = {
              ID: UnitID?.toString(),
              Brand: item.Exist_Brands,
            };

            await createItem(
              (await getLotteryPort()).toString(),
              '/createbrand',
              AddUnitTypes,
            );
          }
        }
      } else {
        const unitTypes = lotteryExists.map(item => ({
          Brand: item.Brand,
        }));

        const unitTypesSet = new Set(unitTypes.map(item => item.Brand));

        const notMatched = existBrands.filter(
          item => !unitTypesSet.has(item.Exist_Brands),
        );

        if (notMatched.length > 0) {
          for (const item of notMatched) {
            if (!isMountedRef.current) break;

            const UnitID = await GetCommonLatestID(
              (await getLotteryPort()).toString(),
              '/GetBrandID',
            );

            const AddUnitTypes: BrandAdd = {
              ID: UnitID?.toString(),
              Brand: item.Exist_Brands,
            };

            await createItem(
              (await getLotteryPort()).toString(),
              '/createbrand',
              AddUnitTypes,
            );
          }
        }
      }
    } catch (error) {
      console.error('Error in handleAdditionalTransferBrand:', error);
    }
  };

  const handleAdditionalTransferSubCat = async () => {
    if (!isMountedRef.current) return;

    try {
      const port = await getInventoryPort();
      const portLottery = await getLotteryPort();

      const [getExistBrands, getLotteryExists] = await Promise.allSettled([
        GetAllItemsNoProps(port, '/getExistsAdditionalSubCat'),
        GetAllItemsNoProps(portLottery, '/GetAllSubCategories'),
      ]);
      console.log('LookupItems');

      if (!isMountedRef.current) return;

      const existBrands =
        getExistBrands.status === 'fulfilled' ? getExistBrands.value : [];
      const lotteryExists =
        getLotteryExists.status === 'fulfilled' ? getLotteryExists.value : [];

      if (lotteryExists === undefined || lotteryExists.length === 0) {
        for (const item of existBrands) {
          if (!isMountedRef.current) break;

          const UnitID = await GetCommonLatestID(
            (await getLotteryPort()).toString(),
            '/GetSubCategoryID',
          );

          const AddUnitTypes: SubCategoryAdd = {
            ID: UnitID?.toString(),
            SubCategory: item.Exist_SubCategories,
          };

          await createItem(
            (await getLotteryPort()).toString(),
            '/createsubcategory',
            AddUnitTypes,
          );
        }
      } else {
        const unitTypes = lotteryExists.map(item => ({
          SubCategory: item.SubCategory,
        }));

        const unitTypesSet = new Set(unitTypes.map(item => item.SubCategory));

        const notMatched = existBrands.filter(
          item => !unitTypesSet.has(item.Exist_SubCategories),
        );

        if (notMatched.length > 0) {
          for (const item of notMatched) {
            if (!isMountedRef.current) break;

            const UnitID = await GetCommonLatestID(
              (await getLotteryPort()).toString(),
              '/GetSubCategoryID',
            );

            const AddUnitTypes: SubCategoryAdd = {
              ID: UnitID?.toString(),
              SubCategory: item.Exist_SubCategories,
            };

            await createItem(
              (await getLotteryPort()).toString(),
              '/createsubcategory',
              AddUnitTypes,
            );
          }
        }
      }
    } catch (error) {
      console.error('Error in handleAdditionalTransferSubCat:', error);
    }
  };

  const handleUnitTypeTransfer = async () => {
    if (!isMountedRef.current) return;

    try {
      const port = await getInventoryPort();
      const portLottery = await getLotteryPort();

      const [getExistUnitTypes, getLotteryExists] = await Promise.allSettled([
        GetAllItemsNoProps(port, '/getExistsUnitTypes'),
        GetAllItemsNoProps(portLottery, '/GetAllUnits'),
      ]);

      if (!isMountedRef.current) return;

      const existUnitTypes =
        getExistUnitTypes.status === 'fulfilled' ? getExistUnitTypes.value : [];
      const lotteryExists =
        getLotteryExists.status === 'fulfilled' ? getLotteryExists.value : [];

      if (lotteryExists === undefined || lotteryExists.length === 0) {
        if (existUnitTypes.length > 0) {
          for (const item of existUnitTypes) {
            if (!isMountedRef.current) break;

            const unitID = await GetCommonLatestID(
              portLottery,
              '/GetUnitTypeID',
            );
            const newUnitType = {
              ID: unitID?.toString(),
              Unit_Type: item.Exist_Unit_Types,
            };
            await createItem(portLottery, '/createunittype', newUnitType);
          }
        }
      } else {
        const unitTypes = lotteryExists.map(item => ({
          Unit_Type: item.Unit_Type,
        }));

        const unitTypesSet = new Set(unitTypes.map(item => item.Unit_Type));

        const notMatched = existUnitTypes.filter(
          item => !unitTypesSet.has(item.Exist_Unit_Types),
        );

        if (notMatched.length > 0) {
          for (const item of notMatched) {
            if (!isMountedRef.current) break;

            const unitID = await GetCommonLatestID(
              portLottery,
              '/GetUnitTypeID',
            );
            const newUnitType = {
              ID: unitID?.toString(),
              Unit_Type: item.Exist_Unit_Types,
            };
            await createItem(portLottery, '/createunittype', newUnitType);
          }
        }
      }
    } catch (error) {
      console.error('Error in handleUnitTypeTransfer:', error);
    }
  };

  // Fetch inventory data with better error handling
  const invNoPage = async () => {
    if (!isMountedRef.current) return;

    try {
      setLoading(true);
      const port = await getInventoryPort();

      const data = await GetAllItemsWithFilter(
        port.toString(),
        '/getInventoryFilter',
        data => {
          if (isMountedRef.current && data) {
            setMasterData(data);
            setFilteredData(data);
            applyPagination(data, 1);
            setInitialLoading(false);
            setLoading(false);
          }
        },
        displayData => {},
        setLoading,
        false,
      );

      if (!isMountedRef.current) return;

      if (data === undefined || !data) {
        showAlertOK(
          'Database Connection Failed, Please Check Your Database Configuration',
          'Connection Failed',
          'OK',
        );
        setInitialLoading(false);
        setLoading(false);
        return;
      }
    } catch (error) {
      console.error('Error fetching inventory:', error);
      if (isMountedRef.current) {
        setInitialLoading(false);
        setLoading(false);
      }
    }
  };

  // Apply filtering based on selected filters
  useEffect(() => {
    if (!isMountedRef.current) return;

    if (
      selectedBrand ||
      selectedDepartment ||
      selectedVendor ||
      selectedSubCategory
    ) {
      setIsEnableFilter(true);
    } else {
      setIsEnableFilter(false);
    }
  }, [selectedBrand, selectedDepartment, selectedVendor, selectedSubCategory]);

  // Apply search debouncing
  useEffect(() => {
    const handler = setTimeout(() => {
      if (isMountedRef.current) {
        setDebouncedQuery(searchQuery);
      }
    }, 300);

    return () => {
      clearTimeout(handler);
    };
  }, [searchQuery]);

  // Apply filters and search
  useEffect(() => {
    if (!isMountedRef.current) return;

    // Skip if masterData isn't loaded yet
    if (masterData.length === 0) return;

    // Reset pagination when filters change
    setPage(1);

    // Filter by department, vendor, brand, and subcategory
    const filtered = masterData.filter(item => {
      const matchesDepartment = selectedDepartment
        ? item.Dept_ID === selectedDepartment
        : true;
      const matchesVendor = selectedVendor
        ? item.Vendor_Number === selectedVendor
        : true;
      const matchesBrand = selectedBrand ? item.Brand === selectedBrand : true;
      const matchesSubCategory = selectedSubCategory
        ? item.SubCategory === selectedSubCategory
        : true;

      // Also apply search filter if we have a query
      const searchTerm = debouncedQuery.toLowerCase();
      const matchesSearch = !debouncedQuery
        ? true
        : (item.ItemNum?.toString().toLowerCase() || '').includes(searchTerm) ||
          (item.ItemName?.toLowerCase() || '').includes(searchTerm) ||
          (item.ItemDescription?.toLowerCase() || '').includes(searchTerm) ||
          (item.SerialNumber?.toLowerCase() || '').includes(searchTerm);

      return (
        matchesDepartment &&
        matchesVendor &&
        matchesBrand &&
        matchesSubCategory &&
        matchesSearch
      );
    });

    setFilteredData(filtered);
    applyPagination(filtered, 1);
  }, [
    selectedDepartment,
    selectedVendor,
    selectedBrand,
    selectedSubCategory,
    debouncedQuery,
    masterData,
  ]);

  // Pagination function
  const applyPagination = (data: Inventory_Filter[], currentPage: number) => {
    if (!isMountedRef.current) return;
    const endIndex = currentPage * ITEMS_PER_PAGE;
    const paginatedItems = data.slice(0, endIndex);
    setDisplayData(paginatedItems);
  };

  // Load more data when reaching the end of the list
  const handleLoadMore = useCallback(() => {
    if (!isMountedRef.current) return;

    if (page * ITEMS_PER_PAGE < filteredData.length) {
      const nextPage = page + 1;
      setPage(nextPage);
      applyPagination(filteredData, nextPage);
    }
  }, [page, filteredData, ITEMS_PER_PAGE]);

  // Apply pagination when page changes
  useEffect(() => {
    if (!isMountedRef.current) return;

    if (filteredData.length > 0 && page > 1) {
      applyPagination(filteredData, page);
    }
  }, [page, filteredData]);

  // Pull to refresh implementation
  const handleRefresh = useCallback(async () => {
    if (!isMountedRef.current) return;

    setRefreshing(true);
    setPage(1);

    try {
      await invNoPage();
    } catch (error) {
      console.error('Error refreshing data:', error);
    }

    if (isMountedRef.current) {
      setRefreshing(false);
    }
  }, []);

  // Fetch departments
  const getInitDept = async () => {
    if (!isMountedRef.current) return;

    try {
      const port = await getInventoryPort();
      GetAllItems<Department[]>(
        port.toString(),
        '/GetDepartments',
        setDepartments,
        setLoading,
      );
    } catch (error) {
      console.error('Error fetching departments:', error);
    }
  };

  // Fetch vendors
  const getVendor = async () => {
    if (!isMountedRef.current) return;

    try {
      const port = await getInventoryPort();
      GetAllItems<Vendor[]>(
        port.toString(),
        '/GetVendors',
        setVendors,
        setLoading,
      );
    } catch (error) {
      console.error('Error fetching vendors:', error);
    }
  };

  // Fetch brands and subcategories
  const getSubCatogoryOrBrand = async () => {
    if (!isMountedRef.current) return;

    try {
      const port = await getInventoryPort();
      const data = await GetAllItemsWithFilter(
        port.toString(),
        '/getInventoryFilterBy_BrandOrCategory',
        SetBrandSubOrCat,
        SetBrandSubOrCat,
        setLoading,
      );

      if (!isMountedRef.current || !data) return;

      const brandOnly = data
        .filter((item: {Brand: string | null}) => item.Brand !== null)
        .map((item: {Brand: string}) => ({Brand: item.Brand}));

      const subCategoriesOnly = data
        .filter(
          (item: {SubCategory: string | null}) => item.SubCategory !== null,
        )
        .map((item: {SubCategory: string}) => ({
          SubCategory: item.SubCategory,
        }));

      setGetBrands(brandOnly);
      setGetSubCategories(subCategoriesOnly);
    } catch (error) {
      console.error('Error fetching brands/categories:', error);
    }
  };

  // Handle item selection with better error handling
  const handleItemPress = useCallback(
    async (item: Inventory_Filter) => {
      if (!isMountedRef.current) return;

      try {
        const isAuthorized = await hasPermission('CFA_Inven_Edit');

        if (!isAuthorized) {
          Alert.alert('You do not have permission to edit inventory.');
          return;
        }

        const port = await getInventoryPort();

        const [vendorItemsResult, inventoryAdditionalResult] =
          await Promise.allSettled([
            GetItemsParamsNoFilter(
              port.toString(),
              '/getvendoritemsbyItemNum/:Vendor_Number/:ItemNum',
              setInventoryVendor,
              {
                Vendor_Number: item.Vendor_Number,
                ItemNum: item.ItemNum,
              },
              false,
            ),
            GetItemsParamsNoFilter(
              port.toString(),
              '/getInventoryAdditional/:ItemNum',
              setInventoryAdditional,
              {
                ItemNum: item.ItemNum,
              },
              false,
            ),
          ]);

        if (!isMountedRef.current) return;

        const vendorItems =
          vendorItemsResult.status === 'fulfilled'
            ? vendorItemsResult.value
            : [];
        const inventoryAdditional =
          inventoryAdditionalResult.status === 'fulfilled'
            ? inventoryAdditionalResult.value
            : [];

        if (item.ItemType === 3) {
          const getBarcode = await GetItemsParamsNoFilterNoReturn(
            port.toString(),
            '/inventory/:ItemNum',
            {ItemNum: item.ItemNum},
          );

          if (isMountedRef.current) {
            navigation.navigate('ChoiceItem', {
              ItemData: getBarcode,
              IsPickList: true,
              ISCREATE: false,
            });
          }
        } else {
          if (route.params?.isFromLottery) {
            if (isMountedRef.current) {
              navigation.navigate('Barcode', {
                ItemData: [item],
                VENDORITEM: vendorItems,
                ADDITIONAL: inventoryAdditional,
                isFromLottery: true,
                CANEDIT: true,
                ISCHOICE: true,
              });
            }
          } else {
            const lotteryDepartment = await AsyncStorage.getItem(
              'LOTTERY_DEP_ID',
            );

            if (!isMountedRef.current) return;

            if (lotteryDepartment) {
              if (lotteryDepartment === item.Dept_ID) {
                navigation.navigate('Barcode', {
                  ItemData: [item],
                  VENDORITEM: vendorItems,
                  ADDITIONAL: inventoryAdditional,
                  CANEDIT: false,
                  ISCHOICE: true,
                });
              } else {
                navigation.navigate('Barcode', {
                  ItemData: [item],
                  VENDORITEM: vendorItems,
                  ADDITIONAL: inventoryAdditional,
                  CANEDIT: true,
                  ISCHOICE: true,
                });
              }
            } else {
              navigation.navigate('Barcode', {
                ItemData: [item],
                VENDORITEM: vendorItems,
                ADDITIONAL: inventoryAdditional,
                CANEDIT: true,
                ISCHOICE: true,
              });
            }
          }
        }
      } catch (error) {
        console.error('Error handling item selection:', error);
        if (isMountedRef.current) {
          Alert.alert('Error', 'Failed to retrieve item details');
        }
      }
    },
    [navigation, route.params?.isFromLottery],
  );

  // Render optimized list item
  const renderItem = useCallback(
    ({item}) => (
      <TouchableWithoutFeedback onPress={handleOutsidePress}>
        <View>
          <InventoryItem item={item} onPress={() => handleItemPress(item)} />
        </View>
      </TouchableWithoutFeedback>
    ),
    [handleItemPress],
  );

  // Toggle keyboard/lookup with better state management
  const toggleLookup = useCallback((value: boolean) => {
    applyPagination(filteredData, page);
    setshowLookup(value);
    setSearchQuery('');
    if (Platform.OS === 'android') {
      if (value) {
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.blur();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 50);
          }
        }, 50);
      } else {
        Keyboard.dismiss();
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.blur();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 50);
          }
        }, 50);
      }
      return;
    }

    // iOS handling
    if (value) {
      setTimeout(() => {
        textInputRef.current?.focus();
      }, 100);
    } else {
      setSearchQuery('');
      Keyboard.dismiss();
    }
  }, []);

  // Custom search function with better error handling
  const handleSearchChange = async (text: string) => {
    if (!isMountedRef.current) return;

    try {
      setCamera(false);
      const getBarcode = await GetItemsParamsNoFilterNoReturn(
        (await getInventoryPort()).toString(),
        '/inventory/:ItemNum',
        {ItemNum: text},
      );

      if (!isMountedRef.current) return;

      setSearchQuery(getBarcode[0]?.ItemNum || text);
      const ScanStatus = await onSearchChange_Common(
        getBarcode[0]?.ItemNum || text,
        filteredData,
        setDisplayData,
        setLoading,
        setSearchQuery,
        navigation,
        showLookup,
        route.params?.isFromLottery,
      );

      if (!isMountedRef.current) return;

      if (!showLookup) {
        if (!ScanStatus) {
          if (textInputRef.current) {
            textInputRef.current.clear();
            textInputRef.current.blur();
          }

          setSearchQuery('');
          Keyboard.dismiss();
          safeSetTimeout(() => {
            if (textInputRef.current && isMountedRef.current) {
              textInputRef.current.focus();
            }
          }, 50);
        }
      }
    } catch (error) {
      console.error('Error in handleSearchChange:', error);
      if (isMountedRef.current) {
        setSearchQuery(text);
      }
    }
  };

  // Optimized key extractor
  const keyExtractor = useCallback(
    (item: Inventory_Filter) => item.ItemNum.toString(),
    [],
  );

  // GetItemLayout for optimized rendering
  const getItemLayout = useCallback(
    (_, index) => ({
      length: 88, // approximate height of item
      offset: 88 * index,
      index,
    }),
    [],
  );

  const handleOutsidePress = useCallback(() => {
    if (!isMountedRef.current) return;

    if (showLookup) {
      // Only trigger when keyboard is ON
      setshowLookup(false);
      Keyboard.dismiss();
      toggleLookup(false); // Turn off keyboard state
    }
  }, [showLookup, toggleLookup]);

  const handleDoneClick = useCallback(() => {
    if (!isMountedRef.current) return;

    setshowLookup(false);
    if (textInputRef.current) {
      textInputRef.current.clear();
      textInputRef.current.blur();
    }

    setSearchQuery('');
    Keyboard.dismiss();
    safeSetTimeout(() => {
      if (textInputRef.current && isMountedRef.current) {
        textInputRef.current.focus();
      }
    }, 50);
    applyPagination(filteredData, page);
  }, [filteredData, page, safeSetTimeout]);

  // Enhanced code scanner with better error handling
  const codeScanner = useCodeScanner({
    codeTypes: [
      'qr',
      'ean-13',
      'upc-a',
      'ean-8',
      'upc-e',
      'code-128',
      'code-39',
      'code-93',
    ],
    onCodeScanned: codes => {
      if (codes.length > 0 && isMountedRef.current) {
        try {
          handleSearchChange(codes[0].value);
        } catch (error) {
          console.error('Error processing scanned code:', error);
        }
      }
    },
  });

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    searchContainer: {
      paddingHorizontal: wp('2.5%'),
      marginBottom: 5,
    },
    headerRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: wp('4%'),
      marginBottom: 5,
    },
    headerTitleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 12,
    },
    headerTitle: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestBold,
      color: colors.text,
    },
    itemCount: {
      fontFamily: Fonts.OnestMedium,
      fontSize: FontSizes.medium,
      color: colors.textSecondary,
    },
    listContainer: {
      flex: 1,
      paddingHorizontal: wp('2.5%'),
    },
    listContent: {
      paddingBottom: hp('10%'),
    },
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: hp('10%'),
    },
    animationContainer: {
      borderRadius: 20,
      backgroundColor: 'transparent',
    },
    lottie: {
      height: 200,
      width: 200,
      backgroundColor: 'transparent',
    },
    emptyText: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: 20,
      fontFamily: Fonts.OnestMedium,
    },
    footer: {
      padding: 15,
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    footerText: {
      marginLeft: 10,
      fontFamily: Fonts.OnestMedium,
      color: colors.textSecondary,
    },
    centered: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: hp('10%'),
    },
    fabContainer: {
      backgroundColor: colors.surface,
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('0.5%'),
      position: 'absolute',
      left: 0,
      right: 0,
      bottom: 0,
      elevation: 12,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: -4},
      shadowOpacity: isDark ? 0.3 : 0.15,
      shadowRadius: 6,
      borderTopWidth: 0,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      height: hp('8%'),
    },
  });

  return (
    <TouchableWithoutFeedback onPress={handleOutsidePress}>
      <View style={styles.container}>
        {/* Header Section */}
        <Header
          NavName={route.params?.isFromLottery ? 'Game Lookup' : 'Item Lookup'}
          isOption={true}
          Options={() => {
            if (!isMountedRef.current) return;

            if (textInputRef.current) {
              textInputRef.current.clear();
              textInputRef.current.blur();
            }

            setSearchQuery('');
            Keyboard.dismiss();
            safeSetTimeout(() => {
              if (textInputRef.current && isMountedRef.current) {
                textInputRef.current.focus();
              }
            }, 50);

            setCamera(!camera);
          }}
        />

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <AppSearchWIthFilter
            OnSearch={handleSearchChange}
            SearchValue={searchQuery}
            OnSearchSet={() => setFilter(true)}
            isEnableFilter={isEnableFilter}
            Keyboardon={showLookup}
            textInputRef={textInputRef}
            onToggleLookup={toggleLookup}
            OnSubmitEditing={handleDoneClick}
          />
        </View>

        {/* Available Items Header */}
        <View style={styles.headerRow}>
          <View style={styles.headerTitleContainer}>
            <Text style={styles.headerTitle}>Available Items</Text>
            <Text style={styles.itemCount}>{` (${filteredData.length})`}</Text>
          </View>
        </View>

        {/* List Container */}
        <View style={styles.listContainer}>
          {initialLoading ? (
            <View style={styles.centered}>
              <AppLoader
                modalVisible={appLoader}
                setModalVisible={setAppLoader}
                isLookup={true}
              />
            </View>
          ) : (
            <FlatList
              ref={flatListRef}
              data={displayData}
              renderItem={renderItem}
              keyExtractor={keyExtractor}
              contentContainerStyle={styles.listContent}
              onEndReached={handleLoadMore}
              onEndReachedThreshold={0.5}
              refreshing={refreshing}
              onRefresh={handleRefresh}
              getItemLayout={getItemLayout}
              ListEmptyComponent={
                <EmptyListComponent
                  searchQuery={searchQuery}
                  IsfromLottery={route.params?.isFromLottery}
                  styles={styles}
                />
              }
              ListFooterComponent={
                <ListFooter
                  loading={loading && !initialLoading}
                  styles={styles}
                />
              }
              initialNumToRender={10}
              maxToRenderPerBatch={10}
              windowSize={10}
              removeClippedSubviews={true}
              updateCellsBatchingPeriod={75}
              showsVerticalScrollIndicator={false}
              maintainVisibleContentPosition={{
                minIndexForVisible: 0,
              }}
            />
          )}
        </View>

        {/* FAB */}
        <View style={styles.fabContainer}>
          <FAB
            label={
              route.params?.isFromLottery ? 'Add New Game' : 'Add New Item'
            }
            position="bottomRight"
            onPress={async () => {
              if (!isMountedRef.current) return;

              try {
                if (route.params?.isFromLottery) {
                  navigation.navigate('Barcode', {
                    isFromLottery: true,
                    LOTTERYDEPT: route.params?.LOTTERYDEPT,
                  });
                } else {
                  const isAuthorized = await hasPermission('CFA_Inven_Add');
                  if (!isAuthorized) {
                    Alert.alert(
                      'You do not have permission to Start/End Shift',
                    );
                    return;
                  }
                  navigation.navigate('ItemType', {IsLookup: true});
                }
              } catch (error) {
                console.error('Error in FAB press:', error);
              }
            }}
          />
        </View>

        {/* Filter Modal */}
        <AppFilter
          isVisible={filter}
          setIsVisble={setFilter}
          Department={text => setSelectedDepartment(text)}
          Vedor={text => setSelectedVendor(text)}
          Brand={text => setSelectedBrand(text)}
          Category={text => setSelectedSubCategory(text)}
          EnableFilter={setIsEnableFilter}
          selectedDepartment={selectedDepartment}
          selectedVendor={selectedVendor}
          selectedBrand={selectedBrand}
          selectedSubCategory={selectedSubCategory}
          isEnableFilter={isEnableFilter}
        />

        <Modal
          animationType="fade"
          transparent={false}
          visible={camera}
          onRequestClose={() => {
            if (isMountedRef.current) {
              setCamera(false);
            }
          }}>
          <AppScanner
            codeScanner={codeScanner}
            onClose={() => {
              if (isMountedRef.current) {
                setCamera(false);
                textInputRef?.current?.focus();
              }
            }}
          />
        </Modal>
      </View>
    </TouchableWithoutFeedback>
  );
};

export default LookupItems;
