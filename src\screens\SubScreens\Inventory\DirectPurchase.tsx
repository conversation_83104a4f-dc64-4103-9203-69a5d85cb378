import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  TextInput,
  Alert,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {
  Backround,
  Primary,
  Secondary,
  SecondaryHint,
} from '../../../constants/Color';
import Header from '../../../components/Inventory/Header';
import AppButton from '../../../components/Inventory/AppButton';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {
  GetAllItems,
  GetAllItemsWithFilter,
  handleSearch,
  showAlert,
} from '../../../utils/PublicHelper';
import {
  Department,
  PurchaseOrderItems,
  VendorItem,
} from '../../../server/types';
import DataList from '../../../components/Inventory/AppList';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import PurchaseOrderItemCart from '../../../components/Inventory/PurchaseOrderItemCart';
import {useFocusEffect} from '@react-navigation/native';
import {getInventoryPort} from '../../../server/InstanceTypes';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AppFocus from '../../../components/Inventory/AppFocus';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts} from '../../../styles/fonts';
import Search from '../../../components/Inventory/Search';
import {MaterialColors} from '../../../constants/MaterialColors';
import FAB from '../../../components/common/FAB';

type NavProps = {
  navigation: NativeStackNavigationProp<any>;
};

const DirectPurchase: React.FC<NavProps> = ({navigation}) => {
  const [purchaseItems, setPurchaseItems] = useState<PurchaseOrderItems[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [vendorItems, setVendorItems] = useState<VendorItem[]>([]);
  const [vendorItemsFilter, setVendorItemsFilter] = useState<VendorItem[]>([]);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  const renderItem = ({item}: {item: PurchaseOrderItems}) => {
    return (
      <View>
        <PurchaseOrderItemCart
          Name={item?.ItemName}
          Ordered={item?.Quan_Ordered}
          RecivedNow={item?.Quan_Received}
          DamagedNow={item.Quan_Damaged}
          OnPress={() =>
            navigation.navigate('PurchaseItemView', {
              ItemData: item,
            })
          }
          Delete={() => removeListItem(item)}
        />
      </View>
    );
  };

  const removeListItem = async (DeletItems: PurchaseOrderItems) => {
    showAlert('Are you sure you want to delete?')
      .then(async result => {
        if (result) {
          const updatedArray = purchaseItems.filter(
            item => item.ItemNum !== DeletItems.ItemNum,
          );
          setPurchaseItems(updatedArray);
          await AsyncStorage.setItem('DIRECTPO', JSON.stringify(updatedArray));
        }
      })
      .catch(error => {
        console.error('Error showing alert', error);
      });
  };
  const onSearchChange = (text: string) => {
    setSearchQuery(text);
    setPage(1);
    handleSearch(
      text,
      vendorItems,
      ['ItemName', 'ItemNum'],
      setVendorItemsFilter,
      setLoading,
    );
  };

  useFocusEffect(
    useCallback(() => {
      vendorDetails();
      getPurchaseItems();
    }, []),
  );

  const getPurchaseItems = async () => {
    const getItems = await AsyncStorage.getItem('DIRECTPO');

    if (getItems === null || getItems === undefined) {
      setPurchaseItems([]); // If no data found, set an empty array
    } else {
      try {
        const parsedItems = JSON.parse(getItems);
        setPurchaseItems(parsedItems);
      } catch (error) {
        console.error('Error parsing purchase items from AsyncStorage', error);
        setPurchaseItems([]);
      }
    }
  };
  const getAllVendorItems = async () => {
    GetAllItems(
      (await getInventoryPort()).toString(),
      '/getAllvendoritems',
      setVendorItems,
      setLoading,
      false,
    );
  };

  const vendorDetails = async () => {
    GetAllItemsWithFilter(
      (await getInventoryPort()).toString(),
      '/getAllvendoritems',
      setVendorItems,
      setVendorItemsFilter,
      setLoading,
      false,
    );
  };

  const handleLoadMore = () => {
    if (!loading) {
      setPage(prevPage => prevPage + 1);
    }
  };
  const isItemNumExist = (itemNum: string) => {
    return purchaseItems.some(item => item.ItemNum === itemNum);
  };

  const VendorItemAdd = (item: VendorItem) => {
    const IsExists = isItemNumExist(item.ItemNum);
    if (IsExists) {
      Alert.alert('Item Already Exists');
    } else {
      setModalVisible(false);
      navigation.navigate('PurchaseItemView', {
        ItemData: item,
        Direct: true,
      });
    }
  };
  const renderModalItem = ({item}: {item: VendorItem}) => {
    return (
      <TouchableOpacity
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          backgroundColor: Secondary,
          marginVertical: hp('0.5%'),
          paddingHorizontal: wp('2.5%'),
          paddingVertical: hp('1.5%'),
          borderRadius: 15,
        }}
        onPress={() => VendorItemAdd(item)}>
        <View style={{gap: 7}}>
          <Text style={{fontFamily: Fonts.OnestBold, fontSize: hp('2%')}}>
            {item.ItemName}
          </Text>
          <Text
            style={{
              fontFamily: Fonts.OnestBold,
              fontSize: hp('2%'),
              color: SecondaryHint,
            }}>
            {`Per Case: ${item.NumPerVenCase}`}
          </Text>
        </View>
        <Text
          style={{
            fontFamily: Fonts.OnestBold,
            fontSize: hp('2.5%'),
            color: Primary,
          }}>
          ${item.CostPer.toFixed(2)}
        </Text>
      </TouchableOpacity>
    );
  };

  const ScanBarcode = (Barcode: string) => {
    const IsExists = isItemNumExist(Barcode);
    if (IsExists) {
      Alert.alert('Item Already Exists');
    } else {
      const GetVendorItem = vendorItems.filter(
        item => item.ItemNum === Barcode,
      );

      setModalVisible(false);
      navigation.navigate('PurchaseItemView', {
        ItemData: GetVendorItem,
        Direct: true,
      });
    }
  };

  return (
    <View
      style={{
        backgroundColor: Backround,
        flex: 1,
        justifyContent: 'space-between',
      }}>
      <View style={{paddingHorizontal: wp('2.5%')}}>
        <Header NavName="Direct Purchase" />
        <Text
          style={{
            paddingVertical: hp('2.5%'),
            fontFamily: Fonts.OnestBold,
            fontSize: hp('2.2%'),
            color: Primary,
          }}>
          {`Items Added: (${purchaseItems.length})`}
        </Text>

        {false && (
          <AppFocus
            PlaceHolder="Scan Book Number"
            Title="Scan Book Number"
            onChangeText={value => {
              ScanBarcode(value);
            }}
          />
        )}

        <DataList
          data={purchaseItems}
          loading={loading}
          renderItem={renderItem}
          Hight="70%"
        />
      </View>

      <View
        style={{
          position: 'absolute',
          right: 0,
          left: 0,
          bottom: 0,
          paddingHorizontal: wp('2.5%'),
          paddingVertical: hp('1%'),
          backgroundColor: MaterialColors.surface,
        }}>
        <View style={{marginVertical: 10}}>
          <AppButton Title="Add Items" OnPress={() => setModalVisible(true)} />
        </View>
        <View style={{marginVertical: 10}}>
          {/* <AppButton
            Title="Save & Close"
            BackRound="#008000"
            OnPress={() => {
              if (purchaseItems.length > 0) {
                navigation.navigate('PurchaseOrderCreate', {
                  ItemData: purchaseItems,
                  IsDirect: true,
                });
              } else {
                Alert.alert('Cant Create PO, Items Empty!');
              }
            }}
          /> */}
          <FAB
            label="Save & Close"
            position="bottomRight"
            onPress={() => {
              if (purchaseItems.length > 0) {
                navigation.navigate('PurchaseOrderCreate', {
                  ItemData: purchaseItems,
                  IsDirect: true,
                });
              } else {
                Alert.alert('Cant Create PO, Items Empty!');
              }
            }}
          />
        </View>
      </View>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}>
        <View style={styles.container}>
          <View
            style={{
              width: '95%',
            }}>
            <Header
              NavName="Add Items"
              Onpress={() => setModalVisible(false)}
              isProvid={true}
            />
          </View>
          {/* search bar */}

          <View style={{paddingBottom: hp('1%')}}>
            <Search
              PlaceHolder="Search..."
              value={searchQuery}
              onChange={onSearchChange}
              AutoFocus={true}
            />

            <Text
              style={{
                fontSize: hp('2%'),
                fontFamily: Fonts.OnestBold,
                color: Primary,
                paddingVertical: hp('1%'),
              }}>{`Total Items: (${vendorItemsFilter.length || 0})`}</Text>
          </View>
          <DataList
            data={vendorItemsFilter}
            keyExtractor={item => String(item.ItemNum)}
            renderItem={renderModalItem}
            loading={loading}
          />
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Backround,
    paddingHorizontal: wp('2.5%'),
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Transparent black background
  },
  tableCell: {
    textAlign: 'left', // Ensures proper alignment for text
    fontSize: 16,
    paddingHorizontal: 5,
  },
  itemNum: {
    width: 150, // Fixed width for ItemNum column
  },
  itemName: {
    flex: 1, // Let ItemName take up the remaining space
  },
  price: {
    width: 80, // Fixed width for Price column
    textAlign: 'right', // Align price to the right
  },
  searchInput: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 5,
    paddingLeft: 10,
    marginBottom: 10,
    marginHorizontal: 10,
  },
});

export default DirectPurchase;
