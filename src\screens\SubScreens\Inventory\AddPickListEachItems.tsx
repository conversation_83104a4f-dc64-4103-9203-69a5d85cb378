import {View, Text, StyleSheet, Alert} from 'react-native';
import React, {useEffect, useState} from 'react';
import {RouteProp} from '@react-navigation/native';
import Header from '../../../components/Inventory/Header';
import {Formik} from 'formik';
import AppButton from '../../../components/Inventory/AppButton';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import * as Yup from 'yup';
import {
  Inventory,
  Invoice_Itemized,
  Invoice_Totals,
} from '../../../server/types';
import {
  calculatePriceWithVAT1,
  calculateVATAmount,
  createData,
  GetAllItems,
  GetItemsParamsNoFilterNoReturn,
  updateData,
} from '../../../utils/PublicHelper';
import {
  applyDefaultsInvoiceItemized,
  applyDefaultsInvoiceTotals,
} from '../../../Validator/Inventory/Barcode';
import {getInventoryPort} from '../../../server/InstanceTypes';
import {Fonts} from '../../../styles/fonts';
import {MaterialColors} from '../../../constants/MaterialColors';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

// Font sizes consistent with the app
const FontSizes = {
  small: 10,
  medium: 12,
  large: 14,
  xLarge: 16,
  xxLarge: 18,
};

type BarcodeScreenRouteProp = RouteProp<any, 'AddPickListEachItems'>;

const validationSchema = Yup.object().shape({
  quanity: Yup.number().required('Quanity is required'),
});

const AddPickListEachItems: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [inventory, setInventory] = useState<Inventory>(route.params?.ItemData);
  const [rates, setRates] = useState<any[]>([]);
  const [success, setSuccess] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [pickListItem, setPickListItem] = useState<Invoice_Totals>(
    route.params?.PickItem,
  );

  const colors = useThemeColors();
  const {isDark} = useTheme();
  const initialValues = {
    quanity: '',
    reason: '',
  };

  useEffect(() => {
    Get_All_Rates();
  }, []);

  const AddPickListItem = async (Values: any) => {
    if (success) {
      Alert.alert('Item Already Added!');
    } else {
      if (Number(Values.quanity) > 0) {
        try {
          ///GetTagalongByItemNum/:ItemNum
          const getBarcode = await GetItemsParamsNoFilterNoReturn(
            (await getInventoryPort()).toString(),
            'GetTagalongByItemNum/:ItemNum',
            {ItemNum: inventory.ItemNum},
          );

          let totalPrice = 0;
          let totalCost = 0;
          let totalVat = 0;
          let grandTotal = 0;

          if (Array.isArray(getBarcode) && getBarcode.length === 0) {
            // No tag-along items, just add the main item
            const result = await AddItems(Values, inventory, false, false);
            if (result) {
              totalPrice += result.Totalprice;
              totalCost += result.TotalCost;
              totalVat += result.TotalVat;
              grandTotal += result.GrandTotale;
            }
          } else {
            // Add the main inventory item first
            const mainResult = await AddItems(Values, inventory, false, false);
            if (mainResult) {
              totalPrice += mainResult.Totalprice;
              totalCost += mainResult.TotalCost;
              totalVat += mainResult.TotalVat;
              grandTotal += mainResult.GrandTotale;
            }

            // Process tag-along items
            const tagAlongItems = getBarcode as any[];
            for (const tagItemNum of tagAlongItems) {
              const tagItem = await GetItemsParamsNoFilterNoReturn(
                (await getInventoryPort()).toString(),
                '/inventory/:ItemNum',
                {ItemNum: tagItemNum.TagAlong_ItemNum},
              );

              const tagResult = await AddItems(
                Values,
                (tagItem as any)[0],
                false,
                false,
              );
              if (tagResult) {
                totalPrice += tagResult.Totalprice;
                totalCost += tagResult.TotalCost;
                totalVat += tagResult.TotalVat;
                grandTotal += tagResult.GrandTotale;
              }
            }
          }

          // Now update invoice totals with accumulated values
          if (totalPrice > 0) {
            const getTotalDetails = await GetItemsParamsNoFilterNoReturn(
              (await getInventoryPort()).toString(),
              '/GetInvoiceTotal/:Invoice_Number',
              {Invoice_Number: pickListItem?.Invoice_Number},
            );

            const TotalPriceWithExist =
              Number((getTotalDetails as any)?.[0]?.Total_Price || 0) +
              totalPrice;
            const TotalCostWithExist =
              Number((getTotalDetails as any)?.[0]?.Total_Cost || 0) +
              totalCost;
            const TotalVatWithExist =
              Number((getTotalDetails as any)?.[0]?.Total_Tax1 || 0) + totalVat;
            const GrandTotalWithExist =
              Number((getTotalDetails as any)?.[0]?.Grand_Total || 0) +
              grandTotal;

            await updateInvoiceTotals(
              TotalPriceWithExist,
              TotalCostWithExist,
              TotalVatWithExist,
              GrandTotalWithExist,
              true, // This will trigger navigation.goBack()
            );
          }
        } catch (error) {
          console.log('Error in AddPickListItem:', error);
        }
      } else {
        Alert.alert('Please Enter Correct Value!');
      }
    }
  };

  const Get_All_Rates = async () => {
    GetAllItems<any[]>(
      (await getInventoryPort()).toString(),
      '/getTaxRates',
      setRates,
      setLoading,
    );
  };
  useEffect(() => {
    console.log(route.params?.PickItem, 'ITEM Here');
  }, []);

  const AddItems = async (
    Values: any,
    inventory: Inventory,
    isTagAlong: boolean = false,
    shouldUpdateTotals: boolean = true,
  ) => {
    try {
      const qtyCost =
        Number(Values.quanity) > 1
          ? Number(inventory.Cost) * Number(Values.quanity)
          : inventory.Cost;
      const qtyPrice =
        Number(Values.quanity) > 1
          ? Number(inventory.Price) * Number(Values.quanity)
          : inventory.Price;

      const TotalCost = Number(qtyCost);

      const VatPrice =
        Number(calculatePriceWithVAT1(Number(qtyPrice), rates[0]?.Tax1_Rate)) -
        Number(qtyPrice);

      const VatPriceForItem = Number(
        calculateVATAmount(Number(inventory.Price), rates[0]?.Tax1_Rate),
      );

      const TotalVat = Number(VatPrice);

      const Totalprice = Number(qtyPrice);

      const GrandTotal = Number(VatPrice) + Number(qtyPrice);
      const GrandTotale = Number(GrandTotal);

      const storeId = await AsyncStorage.getItem('STOREID');
      const invoiceItemized: Partial<Invoice_Itemized> = {
        Invoice_Number: pickListItem?.Invoice_Number,
        ItemNum: inventory.ItemNum,
        Quantity: Number(Values.quanity),
        CostPer: inventory.Cost,
        PricePer: inventory.Price,
        Tax1Per: VatPriceForItem,
        DiffItemName: inventory.ItemName,
        Store_ID: storeId === null ? '1001' : storeId,
        origPricePer: inventory.Price,
        Allow_Discounts: true,
        KitchenQuantityPrinted: 1,
        PricePerBeforeDiscount: inventory.Price,
        SentToKitchen: true,
        Tare: -1,
      };
      const applyDefault = applyDefaultsInvoiceItemized(invoiceItemized);
      console.log(applyDefault, 'Apply Default');

      const createResult = await createData<Invoice_Itemized>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefault,
        endpoint: '/createitemized',
      });

      if (createResult) {
        setSuccess(true);

        // Return the calculated values instead of updating totals here
        return {
          Totalprice,
          TotalCost,
          TotalVat,
          GrandTotale,
        };
      }
    } catch (error) {
      console.log(error);
    }

    return null;
  };

  const updateInvoiceTotals = async (
    Total_Price?: number,
    Total_Cost?: number,
    Total_Tax1?: number,
    Grand_Total?: number,
    isTagAlong?: boolean,
  ) => {
    try {
      const pickListData: Partial<Invoice_Totals> = {
        Invoice_Number: pickListItem.Invoice_Number,
        ReferenceInvoiceNumber: pickListItem.Invoice_Number.toString(),
        Orig_OnHoldID: pickListItem.Orig_OnHoldID,
        Store_ID: pickListItem.Store_ID,
        Cashier_ID: pickListItem.Cashier_ID,
        CustNum: pickListItem.CustNum,
        DateTime: pickListItem.DateTime,
        Total_Price: Total_Price,
        Total_Cost: Total_Cost,
        Total_Tax1: Total_Tax1,
        Grand_Total: Grand_Total,
        Station_ID: pickListItem.Station_ID,
        Payment_Method: pickListItem.Payment_Method,
        Status: pickListItem.Status,
        Taxed_1: pickListItem.Taxed_1,
        Taxed_Sales: Total_Price,
        Dirty: pickListItem.Dirty,
        CourseOrderingProgress: pickListItem.CourseOrderingProgress,
        Total_UndiscountedSale: Total_Price,
      };

      const applyDefult = applyDefaultsInvoiceTotals(pickListData);
      setPickListItem(applyDefult);

      const result = await updateData<Invoice_Totals>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefult,
        endpoint: '/updateinvoice',
      });
      if (result && isTagAlong) {
        navigation.goBack();
      }
    } catch (error) {}
  };

  // Move this INSIDE the component, after the theme hooks
  const styles = StyleSheet.create({
    container: {
      width: '100%',
      height: '100%',
      backgroundColor: colors.background,
      paddingHorizontal: wp('2.5%'),
    },
    itemCard: {
      backgroundColor: colors.card,
      borderRadius: 12,
      paddingHorizontal: wp('4%'),
      paddingVertical: hp('2%'),
      marginTop: hp('2%'),
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    itemName: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.text,
      marginBottom: hp('1%'),
    },
    priceRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    costText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.textSecondary,
      width: wp('30%'),
    },
    priceText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: colors.primary,
    },
    stockText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.textSecondary,
    },
    formContainer: {
      marginTop: hp('3.5%'),
    },
  });
  return (
    <Formik
      initialValues={initialValues}
      enableReinitialize={true}
      validationSchema={validationSchema}
      onSubmit={values => {
        AddPickListItem(values);
      }}>
      {({
        handleChange,
        handleBlur,
        handleSubmit,
        setFieldValue,
        values,
        errors,
        touched,
      }) => (
        <View style={styles.container}>
          <Header NavName="Add Pick List Item" />

          <View style={styles.itemCard}>
            <Text style={styles.itemName}>{inventory.ItemName}</Text>
            <View style={styles.priceRow}>
              <Text style={styles.costText}>Cost: {inventory.Cost}</Text>
              <Text style={styles.priceText}>${inventory.Price}</Text>
            </View>
            <Text style={styles.stockText}>
              Available Stock: {inventory.In_Stock}
            </Text>
          </View>

          <View style={styles.formContainer}>
            <AppTextInput
              PlaceHolder="Enter Quanity"
              Title="Quanity"
              Value={values.quanity}
              onChangeText={handleChange('quanity')}
              onBlur={handleBlur('quanity')}
              error={errors.quanity}
              touched={touched.quanity}
              isNumeric={true}
            />
            <AppButton Title="Save & Close" OnPress={handleSubmit} />
          </View>
        </View>
      )}
    </Formik>
  );
};

export default AddPickListEachItems;
