'use client';

import * as React from 'react';
import {
  Navigation<PERSON>ontainer,
  DefaultTheme,
  DarkTheme,
} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {StatusBar} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import your existing screens
import GetStart from './src/screens/MainScreens/Inventory/GetStart';
import Configure from './src/screens/MainScreens/Inventory/ConfigureIP';
import Login from './src/screens/MainScreens/Inventory/Login';
import Tabs from './src/navigation/bottomNav';
import BarcodeDetails from './src/screens/SubScreens/Inventory/BarcodeDetails';
import ChoiceItem from './src/screens/SubScreens/Inventory/ChoiceItem';
import LookupItems from './src/screens/SubScreens/Inventory/LookupItems';
import AdjustStock from './src/screens/SubScreens/Inventory/AdjustStock';
import CountItem from './src/screens/SubScreens/Inventory/CountItem';
import ItemPickList from './src/screens/SubScreens/Inventory/ItemPickList';
import CreateItemPickList from './src/screens/SubScreens/Inventory/CreateItemPickList';
import AddItemtoPickList from './src/screens/SubScreens/Inventory/AddItemtoPickList';
import PickListItem from './src/screens/SubScreens/Inventory/PickListItem';
import PurchaseOrderItem from './src/screens/SubScreens/Inventory/PurchaseOrderItem';
import PurchaseItemView from './src/screens/SubScreens/Inventory/PurchaseItemView';
import PurchaseOrderCreate from './src/screens/SubScreens/Inventory/PurchaseOrderCreate';
import PurchaseOrderAddItem from './src/screens/SubScreens/Inventory/PurchaseOrderAddItem';
import DirectStoreDeposits from './src/screens/SubScreens/Inventory/DirectStoreDeposits';
import CreateDSD from './src/screens/SubScreens/Inventory/Vendors';
import PendingChanges from './src/screens/SubScreens/Inventory/PendingChanges';
import LOT_InitialSetup from './src/screens/SubScreens/LotterScratch/LOT_InitialSetup';
import LOT_ReviewLastShift from './src/screens/SubScreens/LotterScratch/LOT_ReviewLastShift';
import LOT_ActivateBooks from './src/screens/SubScreens/LotterScratch/LOT_ActivateBooks';
import LOT_OrganizeSlot from './src/screens/SubScreens/LotterScratch/LOT_OrganizeSlot';
import LOT_RemoveAgentConfirm from './src/screens/SubScreens/LotterScratch/LOT_RemoveAgentConfirm';
import LOT_MoveInventory from './src/screens/SubScreens/LotterScratch/LOT_MoveInventory';
import LOT_MovetoInventoryConfirmation from './src/screens/SubScreens/LotterScratch/LOT_MovetoInventoryConfirmation';
import LOT_Inventory from './src/screens/SubScreens/LotterScratch/LOT_Inventory';
import LOT_AddInventory from './src/screens/SubScreens/LotterScratch/LOT_AddInventory';
import LOT_ShiftReport from './src/screens/SubScreens/LotterScratch/LOT_ShiftReport';
import LOT_FinalizeSales from './src/screens/SubScreens/LotterScratch/LOT_FinalizeSales';
import LOT_EndShift from './src/screens/SubScreens/LotterScratch/LOT_EndShift';
import ItemType from './src/screens/SubScreens/Inventory/ItemType';
import ChoiceBarcode from './src/screens/SubScreens/Inventory/ChoiceBarcode';
import PurchaseOrders from './src/screens/SubScreens/Inventory/PurchaseOrder';
import LOT_ConfirmStockBarcode from './src/screens/SubScreens/LotterScratch/LOT_ConfirmStockBarcode';
import LOT_ActivateEachBook from './src/screens/SubScreens/LotterScratch/LOT_ActivateEachBook';
import LOT_Reset from './src/screens/SubScreens/LotterScratch/LOT_Reset';
import LOT_Reset_Each_Shift from './src/screens/SubScreens/LotterScratch/LOT_Reset_Each_Shift';
import LOT_ChangeLocation from './src/screens/SubScreens/LotterScratch/LOT_ChangeLocation';
import ReasonCodes from './src/screens/SubScreens/Inventory/ReasonCodes';
import CreateReasonCode from './src/screens/SubScreens/Inventory/CreateReasonCode';
import ReturnVendor from './src/screens/SubScreens/Inventory/ReturnVendor';
import VendorItems from './src/screens/SubScreens/Inventory/VendorItems';
import AddVendorItemQuanity from './src/screens/SubScreens/Inventory/AddVendorItemQuanity';
import DirectPurchase from './src/screens/SubScreens/Inventory/DirectPurchase';
import PurchaseOrderQuanity from './src/screens/SubScreens/Inventory/PurchaseOrderQuanity';
import AddPickListEachItems from './src/screens/SubScreens/Inventory/AddPickListEachItems';
import Settings from './src/screens/MainScreens/Inventory/Settings';
import TagAlongAdd from './src/screens/SubScreens/Inventory/TagAlongAdd';
import UnitTypes from './src/screens/SubScreens/Inventory/UnitTypes';
import TagAlongs from './src/screens/SubScreens/Inventory/TagAlongs';
import Vendor from './src/screens/SubScreens/Inventory/Vendors';
import Brands from './src/screens/SubScreens/Inventory/Brands';
import Branding from './src/screens/SubScreens/Inventory/Brands';
import SubCategories from './src/screens/SubScreens/Inventory/SubCategories';
import LOT_Setup from './src/screens/SubScreens/LotterScratch/LOT_Setup';
import LOT_Reason from './src/screens/SubScreens/LotterScratch/LOT_ReasonCodes';
import TotalItems from './src/screens/SubScreens/Inventory/Analysis/TotalItems';
import TotalDepartments from './src/screens/SubScreens/Inventory/Analysis/TotalDepartments';
import TotalLotterySales from './src/screens/SubScreens/Inventory/Analysis/TotalLotterySales';
import MissingTickets from './src/screens/SubScreens/Inventory/Analysis/MissingTickets';
import AddNewDepartment from './src/screens/SubScreens/Inventory/Analysis/AddNewDepartment';
import ViewAllTransaction from './src/screens/SubScreens/Inventory/Analysis/ViewAllTransaction';
import Permissions from './src/screens/MainScreens/Inventory/Permissions';
import PRI_PrintPage from './src/screens/SubScreens/PrintLabel/PRI_PrintPage';
import WelcomeScreen from './src/screens/MainScreens/Auth/WelcomeScreen';
import LoginScreen from './src/screens/MainScreens/Auth/LoginScreen';
import RegistrationScreen from './src/screens/MainScreens/Auth/RegistrationScreen';
import Users from './src/screens/MainScreens/Inventory/Users';
import UserSettings from './src/screens/MainScreens/Inventory/UserSettings';
import ForgotPasswordScreen from './src/screens/MainScreens/Auth/ForgotPasswordScreen';
import OTPValidationScreen from './src/screens/MainScreens/Auth/OTPValidationScreen';
import ResetPasswordScreen from './src/screens/MainScreens/Auth/ResetPasswordScreen';
import NotificationSettings from './src/screens/SubScreens/Notifications/NotificationSettings';
import LabelMarginConfig from './src/screens/SubScreens/PrintLabel/LabelMarginConfig';

// Import Theme Provider
import {ThemeProvider, useTheme} from './src/Theme/ThemeContext';

const Stack = createNativeStackNavigator();

// Custom Navigation Themes
const CustomLightTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: '#007AFF',
    background: '#FFFFFF',
    card: '#FFFFFF',
    text: '#000000',
    border: '#E5E5E5',
    notification: '#FF3B30',
  },
};

const CustomDarkTheme = {
  ...DarkTheme,
  colors: {
    ...DarkTheme.colors,
    primary: '#0A84FF',
    background: '#000000',
    card: '#1C1C1E',
    text: '#FFFFFF',
    border: '#38383A',
    notification: '#FF453A',
  },
};

const AppNavigator = () => {
  const [initialRoute, setInitialRoute] = React.useState<string | null>(null);
  const {isDark} = useTheme();

  React.useEffect(() => {
    const checkInitialRoute = async () => {
      try {
        const localIp = await AsyncStorage.getItem('LOCALIP');
        const token = await AsyncStorage.getItem('userToken');

        if (token) {
          if (
            !localIp ||
            localIp === undefined ||
            localIp === null ||
            localIp === ''
          ) {
            setInitialRoute('ConfigureIp');
          } else {
            setInitialRoute('Home');
          }
        } else {
          const isUser = await AsyncStorage.getItem('ISEMPLOYEELOGGED');
          if (isUser) {
            const parsedOrgData = JSON.parse(isUser);
            if (parsedOrgData) {
              setInitialRoute('Home');
            } else {
              setInitialRoute('Welcome');
            }
          } else {
            setInitialRoute('Welcome');
          }
        }
      } catch (error) {
        console.error('Error checking initial route:', error);
        setInitialRoute('Welcome');
      }
    };

    checkInitialRoute();
  }, []);

  if (initialRoute === null) {
    return null; // Or a loading spinner
  }

  return (
    <>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor={isDark ? '#000000' : '#FFFFFF'}
      />
      <NavigationContainer theme={isDark ? CustomDarkTheme : CustomLightTheme}>
        <Stack.Navigator
          initialRouteName={initialRoute}
          screenOptions={{
            headerStyle: {
              backgroundColor: isDark ? '#1C1C1E' : '#FFFFFF',
            },
            headerTintColor: isDark ? '#FFFFFF' : '#000000',
            headerTitleStyle: {
              color: isDark ? '#FFFFFF' : '#000000',
            },
          }}>
          <Stack.Screen
            name="Welcome"
            component={WelcomeScreen}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="LoginScreen"
            component={LoginScreen}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ForgotPassword"
            component={ForgotPasswordScreen}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="OTPValidation"
            component={OTPValidationScreen}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ResetPassword"
            component={ResetPasswordScreen}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="UserSettings"
            component={UserSettings}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="RegistrationScreen"
            component={RegistrationScreen}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="GetStart"
            component={GetStart}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="TotalItems"
            component={TotalItems}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="TotalLotterySales"
            component={TotalLotterySales}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ViewAllTransaction"
            component={ViewAllTransaction}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="NewDepartment"
            component={AddNewDepartment}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ToatalMissings"
            component={MissingTickets}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="TotalDeparments"
            component={TotalDepartments}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ConfigureIp"
            component={Configure}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="Login"
            component={Login}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="Home"
            component={Tabs}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="Settings"
            component={Settings}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="UnitType"
            component={UnitTypes}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="Brands"
            component={Branding}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="PrintPage"
            component={PRI_PrintPage}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="LabelMarginConfig"
            component={LabelMarginConfig}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="Permissions"
            component={Permissions}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="Users"
            component={Users}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="SubCategory"
            component={SubCategories}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="AddTagAlong"
            component={TagAlongAdd}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="Barcode"
            component={BarcodeDetails}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="TagAlongs"
            component={TagAlongs}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ItemType"
            component={ItemType}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ChoiceItem"
            component={ChoiceItem}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ChoiceBarcode"
            component={ChoiceBarcode}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="Lookup"
            component={LookupItems}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="AdjustStock"
            component={AdjustStock}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="CountItem"
            component={CountItem}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ItemPickList"
            component={ItemPickList}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="CreatePickList"
            component={CreateItemPickList}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="AddItemPickList"
            component={AddItemtoPickList}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="AddPickListEachItems"
            component={AddPickListEachItems}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="PickListItem"
            component={PickListItem}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="PurchaseOrderQuanity"
            component={PurchaseOrderQuanity}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ReasonCode"
            component={ReasonCodes}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="CreateReasonCode"
            component={CreateReasonCode}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="PurchaseOrder"
            component={PurchaseOrders}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="PurchaseOrderItem"
            component={PurchaseOrderItem}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="PurchaseItemView"
            component={PurchaseItemView}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="PurchaseOrderCreate"
            component={PurchaseOrderCreate}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="PurchaseAddItem"
            component={PurchaseOrderAddItem}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="DirectStoreDeposits"
            component={DirectStoreDeposits}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ReturnToVendor"
            component={ReturnVendor}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="VendorItems"
            component={VendorItems}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="AddVendorItemQuanity"
            component={AddVendorItemQuanity}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="DirectPurchase"
            component={DirectPurchase}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="Vendors"
            component={Vendor}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="PendingChanges"
            component={PendingChanges}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="IntialSetup"
            component={LOT_InitialSetup}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="LotterySetup"
            component={LOT_Setup}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="NotificationSettings"
            component={NotificationSettings}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="LotteryReason"
            component={LOT_Reason}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ConfirmBarcodes"
            component={LOT_ConfirmStockBarcode}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ReviewLastShift"
            component={LOT_ReviewLastShift}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ActivaBooks"
            component={LOT_ActivateBooks}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ActivateByBooks"
            component={LOT_ActivateEachBook}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="OrganizeSlot"
            component={LOT_OrganizeSlot}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ResetShift"
            component={LOT_Reset}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ResetShiftEach"
            component={LOT_Reset_Each_Shift}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ChangeLocation"
            component={LOT_ChangeLocation}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="RemoveAgentConfirm"
            component={LOT_RemoveAgentConfirm}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="MoveInventory"
            component={LOT_MoveInventory}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="MoveInventorytConfirm"
            component={LOT_MovetoInventoryConfirmation}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="Inventory"
            component={LOT_Inventory}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="AddInventory"
            component={LOT_AddInventory}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ShiftReport"
            component={LOT_ShiftReport}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="FinalizePage"
            component={LOT_FinalizeSales}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="EndShift"
            component={LOT_EndShift}
            options={{headerShown: false}}
          />
        </Stack.Navigator>
      </NavigationContainer>
    </>
  );
};

const App = () => {
  return (
    <ThemeProvider>
      <AppNavigator />
    </ThemeProvider>
  );
};

export default App;
